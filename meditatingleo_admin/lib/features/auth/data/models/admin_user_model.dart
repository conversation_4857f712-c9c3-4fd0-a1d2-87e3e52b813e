import 'package:equatable/equatable.dart';

/// Enum representing different admin roles with their permission levels
enum AdminRole {
  contentManager,
  systemAdmin,
  superAdmin,
}

/// Extension to provide role-based permission checks
extension AdminRolePermissions on AdminRole {
  /// Whether this role can manage content (journeys, prompts)
  bool get canManageContent {
    switch (this) {
      case AdminRole.contentManager:
      case AdminRole.systemAdmin:
      case AdminRole.superAdmin:
        return true;
    }
  }

  /// Whether this role can manage users
  bool get canManageUsers {
    switch (this) {
      case AdminRole.contentManager:
        return false;
      case AdminRole.systemAdmin:
      case AdminRole.superAdmin:
        return true;
    }
  }

  /// Whether this role can manage system settings
  bool get canManageSystem {
    switch (this) {
      case AdminRole.contentManager:
      case AdminRole.systemAdmin:
        return false;
      case AdminRole.superAdmin:
        return true;
    }
  }
}

/// [AdminUserModel] represents an admin user with role-based permissions.
///
/// Used for authentication and authorization in the admin panel.
/// Supports three role levels: Content Manager, System Admin, and Super Admin.
class AdminUserModel extends Equatable {
  /// Creates an [AdminUserModel] instance.
  const AdminUserModel({
    required this.id,
    required this.email,
    required this.role,
    required this.isActive,
    this.lastLoginAt,
    required this.createdAt,
    required this.updatedAt,
  });

  final String id;
  final String email;
  final AdminRole role;
  final bool isActive;
  final String? lastLoginAt;
  final String createdAt;
  final String updatedAt;

  /// Creates an [AdminUserModel] from JSON.
  factory AdminUserModel.fromJson(Map<String, dynamic> json) {
    AdminRole parseRole(String roleString) {
      switch (roleString) {
        case 'content_manager':
          return AdminRole.contentManager;
        case 'system_admin':
          return AdminRole.systemAdmin;
        case 'super_admin':
          return AdminRole.superAdmin;
        default:
          return AdminRole.contentManager;
      }
    }

    return AdminUserModel(
      id: json['id'] as String,
      email: json['email'] as String,
      role: parseRole(json['role'] as String),
      isActive: json['is_active'] as bool,
      lastLoginAt: json['last_login_at'] as String?,
      createdAt: json['created_at'] as String,
      updatedAt: json['updated_at'] as String,
    );
  }

  /// Converts [AdminUserModel] to JSON.
  Map<String, dynamic> toJson() {
    String roleToString(AdminRole role) {
      switch (role) {
        case AdminRole.contentManager:
          return 'content_manager';
        case AdminRole.systemAdmin:
          return 'system_admin';
        case AdminRole.superAdmin:
          return 'super_admin';
      }
    }

    return {
      'id': id,
      'email': email,
      'role': roleToString(role),
      'is_active': isActive,
      'last_login_at': lastLoginAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  /// Creates a copy of this [AdminUserModel] with the given fields replaced.
  AdminUserModel copyWith({
    String? id,
    String? email,
    AdminRole? role,
    bool? isActive,
    String? lastLoginAt,
    String? createdAt,
    String? updatedAt,
  }) {
    return AdminUserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        role,
        isActive,
        lastLoginAt,
        createdAt,
        updatedAt,
      ];
}

/// Extension to provide permission checks on AdminUserModel instances
extension AdminUserPermissions on AdminUserModel {
  /// Whether this user can manage content
  bool get canManageContent => role.canManageContent;

  /// Whether this user can manage users
  bool get canManageUsers => role.canManageUsers;

  /// Whether this user can manage system settings
  bool get canManageSystem => role.canManageSystem;
}
