import 'package:equatable/equatable.dart';

/// [MfaSetupModel] represents multi-factor authentication setup information.
///
/// Contains the secret, QR code URL, backup codes, and status for MFA setup.
/// Used during MFA configuration and management.
class MfaSetupModel extends Equatable {
  /// Creates an [MfaSetupModel] instance.
  const MfaSetupModel({
    required this.secret,
    required this.qrCodeUrl,
    required this.backupCodes,
    required this.isEnabled,
  });

  final String secret;
  final String qrCodeUrl;
  final List<String> backupCodes;
  final bool isEnabled;

  /// Creates an [MfaSetupModel] from JSON.
  factory MfaSetupModel.fromJson(Map<String, dynamic> json) {
    return MfaSetupModel(
      secret: json['secret'] as String,
      qrCodeUrl: json['qr_code_url'] as String,
      backupCodes: (json['backup_codes'] as List<dynamic>).cast<String>(),
      isEnabled: json['is_enabled'] as bool,
    );
  }

  /// Converts [MfaSetupModel] to JSON.
  Map<String, dynamic> toJson() {
    return {
      'secret': secret,
      'qr_code_url': qrCodeUrl,
      'backup_codes': backupCodes,
      'is_enabled': isEnabled,
    };
  }

  /// Creates a copy of this [MfaSetupModel] with the given fields replaced.
  MfaSetupModel copyWith({
    String? secret,
    String? qrCodeUrl,
    List<String>? backupCodes,
    bool? isEnabled,
  }) {
    return MfaSetupModel(
      secret: secret ?? this.secret,
      qrCodeUrl: qrCodeUrl ?? this.qrCodeUrl,
      backupCodes: backupCodes ?? this.backupCodes,
      isEnabled: isEnabled ?? this.isEnabled,
    );
  }

  @override
  List<Object?> get props => [
        secret,
        qrCodeUrl,
        backupCodes,
        isEnabled,
      ];
}

/// Extension to provide validation and utility methods
extension MfaSetupValidation on MfaSetupModel {
  /// Whether the secret is valid (not empty and proper length)
  bool get hasValidSecret {
    return secret.isNotEmpty && secret.length >= 16;
  }

  /// Whether the QR code URL is valid
  bool get hasValidQrCode {
    return qrCodeUrl.isNotEmpty && qrCodeUrl.startsWith('otpauth://');
  }

  /// Whether backup codes are valid (at least 2 codes, each 6 digits)
  bool get hasValidBackupCodes {
    if (backupCodes.length < 2) return false;

    for (final code in backupCodes) {
      if (code.length != 6 || !RegExp(r'^\d{6}$').hasMatch(code)) {
        return false;
      }
    }
    return true;
  }

  /// Whether the complete setup is valid
  bool get isValidSetup {
    return hasValidSecret && hasValidQrCode && hasValidBackupCodes;
  }

  /// Number of backup codes available
  int get backupCodesCount => backupCodes.length;

  /// Whether backup codes are available
  bool get hasBackupCodes => backupCodes.isNotEmpty;
}
