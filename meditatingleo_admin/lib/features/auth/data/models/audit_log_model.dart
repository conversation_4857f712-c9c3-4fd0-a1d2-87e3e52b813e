import 'package:equatable/equatable.dart';

/// Enum representing different audit actions
enum AuditAction {
  login,
  logout,
  loginFailed,
  mfaSetup,
  mfaVerified,
  mfaFailed,
  passwordChanged,
  roleChanged,
  accountLocked,
  accountUnlocked,
}

/// Enum representing audit severity levels
enum AuditSeverity {
  low,
  medium,
  high,
}

/// [AuditLogModel] represents a security audit log entry.
///
/// Used for tracking authentication events, security incidents, and admin actions.
/// Provides comprehensive audit trail for compliance and security monitoring.
class AuditLogModel extends Equatable {
  /// Creates an [AuditLogModel] instance.
  const AuditLogModel({
    required this.id,
    required this.userId,
    required this.action,
    required this.details,
    required this.timestamp,
    required this.success,
    this.errorMessage,
  });

  final String id;
  final String userId;
  final AuditAction action;
  final Map<String, dynamic> details;
  final String timestamp;
  final bool success;
  final String? errorMessage;

  /// Creates an [AuditLogModel] from JSON.
  factory AuditLogModel.fromJson(Map<String, dynamic> json) {
    AuditAction parseAction(String actionString) {
      switch (actionString) {
        case 'login':
          return AuditAction.login;
        case 'logout':
          return AuditAction.logout;
        case 'login_failed':
          return AuditAction.loginFailed;
        case 'mfa_setup':
          return AuditAction.mfaSetup;
        case 'mfa_verified':
          return AuditAction.mfaVerified;
        case 'mfa_failed':
          return AuditAction.mfaFailed;
        case 'password_changed':
          return AuditAction.passwordChanged;
        case 'role_changed':
          return AuditAction.roleChanged;
        case 'account_locked':
          return AuditAction.accountLocked;
        case 'account_unlocked':
          return AuditAction.accountUnlocked;
        default:
          return AuditAction.login;
      }
    }

    return AuditLogModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      action: parseAction(json['action'] as String),
      details: json['details'] as Map<String, dynamic>,
      timestamp: json['timestamp'] as String,
      success: json['success'] as bool,
      errorMessage: json['error_message'] as String?,
    );
  }

  /// Converts [AuditLogModel] to JSON.
  Map<String, dynamic> toJson() {
    String actionToString(AuditAction action) {
      switch (action) {
        case AuditAction.login:
          return 'login';
        case AuditAction.logout:
          return 'logout';
        case AuditAction.loginFailed:
          return 'login_failed';
        case AuditAction.mfaSetup:
          return 'mfa_setup';
        case AuditAction.mfaVerified:
          return 'mfa_verified';
        case AuditAction.mfaFailed:
          return 'mfa_failed';
        case AuditAction.passwordChanged:
          return 'password_changed';
        case AuditAction.roleChanged:
          return 'role_changed';
        case AuditAction.accountLocked:
          return 'account_locked';
        case AuditAction.accountUnlocked:
          return 'account_unlocked';
      }
    }

    return {
      'id': id,
      'user_id': userId,
      'action': actionToString(action),
      'details': details,
      'timestamp': timestamp,
      'success': success,
      'error_message': errorMessage,
    };
  }

  /// Creates a copy of this [AuditLogModel] with the given fields replaced.
  AuditLogModel copyWith({
    String? id,
    String? userId,
    AuditAction? action,
    Map<String, dynamic>? details,
    String? timestamp,
    bool? success,
    String? errorMessage,
  }) {
    return AuditLogModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      action: action ?? this.action,
      details: details ?? this.details,
      timestamp: timestamp ?? this.timestamp,
      success: success ?? this.success,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        action,
        details,
        timestamp,
        success,
        errorMessage,
      ];
}

/// Extension to provide analysis and utility methods
extension AuditLogAnalysis on AuditLogModel {
  /// Whether this is a security-related event
  bool get isSecurityEvent {
    switch (action) {
      case AuditAction.loginFailed:
      case AuditAction.mfaFailed:
      case AuditAction.accountLocked:
      case AuditAction.passwordChanged:
      case AuditAction.roleChanged:
        return true;
      case AuditAction.login:
      case AuditAction.logout:
      case AuditAction.mfaSetup:
      case AuditAction.mfaVerified:
      case AuditAction.accountUnlocked:
        return false;
    }
  }

  /// Whether this is an authentication-related event
  bool get isAuthenticationEvent {
    switch (action) {
      case AuditAction.login:
      case AuditAction.logout:
      case AuditAction.loginFailed:
      case AuditAction.mfaVerified:
      case AuditAction.mfaFailed:
        return true;
      case AuditAction.mfaSetup:
      case AuditAction.passwordChanged:
      case AuditAction.roleChanged:
      case AuditAction.accountLocked:
      case AuditAction.accountUnlocked:
        return false;
    }
  }

  /// Whether this is an MFA-related event
  bool get isMfaEvent {
    switch (action) {
      case AuditAction.mfaSetup:
      case AuditAction.mfaVerified:
      case AuditAction.mfaFailed:
        return true;
      case AuditAction.login:
      case AuditAction.logout:
      case AuditAction.loginFailed:
      case AuditAction.passwordChanged:
      case AuditAction.roleChanged:
      case AuditAction.accountLocked:
      case AuditAction.accountUnlocked:
        return false;
    }
  }

  /// Extract IP address from details
  String? get ipAddress => details['ip_address'] as String?;

  /// Extract user agent from details
  String? get userAgent => details['user_agent'] as String?;

  /// Determine the severity of this audit event
  AuditSeverity get severity {
    switch (action) {
      case AuditAction.loginFailed:
      case AuditAction.mfaFailed:
      case AuditAction.accountLocked:
      case AuditAction.roleChanged:
        return AuditSeverity.high;
      case AuditAction.passwordChanged:
      case AuditAction.mfaSetup:
      case AuditAction.accountUnlocked:
        return AuditSeverity.medium;
      case AuditAction.login:
      case AuditAction.logout:
      case AuditAction.mfaVerified:
        return AuditSeverity.low;
    }
  }
}
