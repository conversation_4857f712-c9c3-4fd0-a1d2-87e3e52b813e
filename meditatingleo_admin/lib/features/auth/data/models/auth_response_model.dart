import 'package:equatable/equatable.dart';
import 'admin_user_model.dart';

/// [AuthResponseModel] represents the response from authentication operations.
///
/// Contains user information, tokens, and MFA status for admin authentication.
/// Used throughout the admin authentication flow.
class AuthResponseModel extends Equatable {
  /// Creates an [AuthResponseModel] instance.
  const AuthResponseModel({
    required this.user,
    required this.accessToken,
    required this.refreshToken,
    required this.expiresAt,
    required this.requiresMfa,
    this.mfaToken,
  });

  final AdminUserModel user;
  final String accessToken;
  final String refreshToken;
  final String expiresAt;
  final bool requiresMfa;
  final String? mfaToken;

  /// Creates an [AuthResponseModel] from JSON.
  factory AuthResponseModel.fromJson(Map<String, dynamic> json) {
    return AuthResponseModel(
      user: AdminUserModel.fromJson(json['user'] as Map<String, dynamic>),
      accessToken: json['access_token'] as String,
      refreshToken: json['refresh_token'] as String,
      expiresAt: json['expires_at'] as String,
      requiresMfa: json['requires_mfa'] as bool,
      mfaToken: json['mfa_token'] as String?,
    );
  }

  /// Converts [AuthResponseModel] to JSON.
  Map<String, dynamic> toJson() {
    return {
      'user': user.toJson(),
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'expires_at': expiresAt,
      'requires_mfa': requiresMfa,
      'mfa_token': mfaToken,
    };
  }

  /// Creates a copy of this [AuthResponseModel] with the given fields replaced.
  AuthResponseModel copyWith({
    AdminUserModel? user,
    String? accessToken,
    String? refreshToken,
    String? expiresAt,
    bool? requiresMfa,
    String? mfaToken,
  }) {
    return AuthResponseModel(
      user: user ?? this.user,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      expiresAt: expiresAt ?? this.expiresAt,
      requiresMfa: requiresMfa ?? this.requiresMfa,
      mfaToken: mfaToken ?? this.mfaToken,
    );
  }

  @override
  List<Object?> get props => [
        user,
        accessToken,
        refreshToken,
        expiresAt,
        requiresMfa,
        mfaToken,
      ];
}

/// Extension to provide validation and utility methods
extension AuthResponseValidation on AuthResponseModel {
  /// Whether the response contains valid tokens
  bool get hasValidTokens {
    return accessToken.isNotEmpty && refreshToken.isNotEmpty;
  }

  /// Whether the response has an MFA token
  bool get hasMfaToken {
    return mfaToken != null && mfaToken!.isNotEmpty;
  }
}
