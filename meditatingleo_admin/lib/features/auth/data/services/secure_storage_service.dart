import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:meditatingleo_admin/core/error/exceptions.dart';

/// Service for secure storage of sensitive authentication data.
///
/// Provides encrypted storage for tokens, user preferences, and other
/// sensitive information using the device's secure storage capabilities.
class SecureStorageService {
  /// Creates a [SecureStorageService] instance.
  const SecureStorageService();

  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Storage keys
  static const String _accessTokenKey = 'admin_access_token';
  static const String _refreshTokenKey = 'admin_refresh_token';
  static const String _userIdKey = 'admin_user_id';
  static const String _userEmailKey = 'admin_user_email';
  static const String _userRoleKey = 'admin_user_role';
  static const String _mfaEnabledKey = 'admin_mfa_enabled';
  static const String _lastLoginKey = 'admin_last_login';
  static const String _sessionExpiryKey = 'admin_session_expiry';
  static const String _biometricEnabledKey = 'admin_biometric_enabled';

  /// Stores authentication tokens securely.
  ///
  /// Throws [DatabaseException] if storage fails.
  Future<void> storeAuthTokens({
    required String accessToken,
    required String refreshToken,
    required String expiresAt,
  }) async {
    try {
      await Future.wait([
        _storage.write(key: _accessTokenKey, value: accessToken),
        _storage.write(key: _refreshTokenKey, value: refreshToken),
        _storage.write(key: _sessionExpiryKey, value: expiresAt),
      ]);
    } catch (e) {
      throw DatabaseException('Failed to store auth tokens: ${e.toString()}');
    }
  }

  /// Retrieves the stored access token.
  ///
  /// Returns null if no token is stored.
  Future<String?> getAccessToken() async {
    try {
      return await _storage.read(key: _accessTokenKey);
    } catch (e) {
      return null;
    }
  }

  /// Retrieves the stored refresh token.
  ///
  /// Returns null if no token is stored.
  Future<String?> getRefreshToken() async {
    try {
      return await _storage.read(key: _refreshTokenKey);
    } catch (e) {
      return null;
    }
  }

  /// Retrieves the session expiry time.
  ///
  /// Returns null if no expiry is stored.
  Future<String?> getSessionExpiry() async {
    try {
      return await _storage.read(key: _sessionExpiryKey);
    } catch (e) {
      return null;
    }
  }

  /// Stores user information securely.
  ///
  /// Throws [DatabaseException] if storage fails.
  Future<void> storeUserInfo({
    required String userId,
    required String email,
    required String role,
    required bool mfaEnabled,
  }) async {
    try {
      await Future.wait([
        _storage.write(key: _userIdKey, value: userId),
        _storage.write(key: _userEmailKey, value: email),
        _storage.write(key: _userRoleKey, value: role),
        _storage.write(key: _mfaEnabledKey, value: mfaEnabled.toString()),
        _storage.write(key: _lastLoginKey, value: DateTime.now().toIso8601String()),
      ]);
    } catch (e) {
      throw DatabaseException('Failed to store user info: ${e.toString()}');
    }
  }

  /// Retrieves the stored user ID.
  ///
  /// Returns null if no user ID is stored.
  Future<String?> getUserId() async {
    try {
      return await _storage.read(key: _userIdKey);
    } catch (e) {
      return null;
    }
  }

  /// Retrieves the stored user email.
  ///
  /// Returns null if no email is stored.
  Future<String?> getUserEmail() async {
    try {
      return await _storage.read(key: _userEmailKey);
    } catch (e) {
      return null;
    }
  }

  /// Retrieves the stored user role.
  ///
  /// Returns null if no role is stored.
  Future<String?> getUserRole() async {
    try {
      return await _storage.read(key: _userRoleKey);
    } catch (e) {
      return null;
    }
  }

  /// Retrieves the MFA enabled status.
  ///
  /// Returns false if no status is stored.
  Future<bool> isMfaEnabled() async {
    try {
      final value = await _storage.read(key: _mfaEnabledKey);
      return value == 'true';
    } catch (e) {
      return false;
    }
  }

  /// Retrieves the last login time.
  ///
  /// Returns null if no login time is stored.
  Future<String?> getLastLogin() async {
    try {
      return await _storage.read(key: _lastLoginKey);
    } catch (e) {
      return null;
    }
  }

  /// Stores biometric authentication preference.
  ///
  /// Throws [DatabaseException] if storage fails.
  Future<void> setBiometricEnabled(bool enabled) async {
    try {
      await _storage.write(key: _biometricEnabledKey, value: enabled.toString());
    } catch (e) {
      throw DatabaseException('Failed to store biometric preference: ${e.toString()}');
    }
  }

  /// Retrieves the biometric authentication preference.
  ///
  /// Returns false if no preference is stored.
  Future<bool> isBiometricEnabled() async {
    try {
      final value = await _storage.read(key: _biometricEnabledKey);
      return value == 'true';
    } catch (e) {
      return false;
    }
  }

  /// Checks if the current session is valid (not expired).
  ///
  /// Returns true if session is valid, false otherwise.
  Future<bool> isSessionValid() async {
    try {
      final expiryString = await getSessionExpiry();
      if (expiryString == null) return false;

      final expiry = DateTime.parse(expiryString);
      final now = DateTime.now();
      
      // Add a 5-minute buffer before expiry
      final bufferTime = expiry.subtract(const Duration(minutes: 5));
      
      return now.isBefore(bufferTime);
    } catch (e) {
      return false;
    }
  }

  /// Checks if stored authentication data exists.
  ///
  /// Returns true if both access and refresh tokens are stored.
  Future<bool> hasStoredAuth() async {
    try {
      final accessToken = await getAccessToken();
      final refreshToken = await getRefreshToken();
      
      return accessToken != null && 
             refreshToken != null && 
             accessToken.isNotEmpty && 
             refreshToken.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Clears all stored authentication data.
  ///
  /// Used during logout or when authentication becomes invalid.
  Future<void> clearAuthData() async {
    try {
      await Future.wait([
        _storage.delete(key: _accessTokenKey),
        _storage.delete(key: _refreshTokenKey),
        _storage.delete(key: _sessionExpiryKey),
        _storage.delete(key: _userIdKey),
        _storage.delete(key: _userEmailKey),
        _storage.delete(key: _userRoleKey),
        _storage.delete(key: _mfaEnabledKey),
        _storage.delete(key: _lastLoginKey),
      ]);
    } catch (e) {
      // Don't throw exception for cleanup operations
      print('Failed to clear auth data: ${e.toString()}');
    }
  }

  /// Clears all stored data.
  ///
  /// Used for complete reset or uninstallation.
  Future<void> clearAllData() async {
    try {
      await _storage.deleteAll();
    } catch (e) {
      // Don't throw exception for cleanup operations
      print('Failed to clear all data: ${e.toString()}');
    }
  }

  /// Gets all stored keys for debugging purposes.
  ///
  /// Should only be used in development/debug mode.
  Future<Map<String, String>> getAllStoredData() async {
    try {
      return await _storage.readAll();
    } catch (e) {
      return {};
    }
  }

  /// Stores a custom key-value pair securely.
  ///
  /// Useful for storing additional secure preferences.
  /// Throws [DatabaseException] if storage fails.
  Future<void> storeCustomData(String key, String value) async {
    try {
      await _storage.write(key: 'custom_$key', value: value);
    } catch (e) {
      throw DatabaseException('Failed to store custom data: ${e.toString()}');
    }
  }

  /// Retrieves a custom stored value.
  ///
  /// Returns null if the key doesn't exist.
  Future<String?> getCustomData(String key) async {
    try {
      return await _storage.read(key: 'custom_$key');
    } catch (e) {
      return null;
    }
  }

  /// Deletes a custom stored value.
  Future<void> deleteCustomData(String key) async {
    try {
      await _storage.delete(key: 'custom_$key');
    } catch (e) {
      // Don't throw exception for cleanup operations
      print('Failed to delete custom data: ${e.toString()}');
    }
  }
}
