import 'dart:math';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:meditatingleo_admin/features/auth/data/models/mfa_setup_model.dart';
import 'package:meditatingleo_admin/core/error/exceptions.dart';

/// Service for handling multi-factor authentication operations.
///
/// Provides TOTP-based MFA setup, verification, and management for admin users.
/// Includes backup code generation and validation.
class MfaService {
  /// Creates an [MfaService] instance.
  const MfaService(this._supabaseClient);

  final SupabaseClient _supabaseClient;

  /// Sets up MFA for a user.
  ///
  /// Generates a secret, QR code URL, and backup codes.
  /// Returns [MfaSetupModel] with setup information.
  /// Throws [MfaException] if setup fails.
  Future<MfaSetupModel> setupMfa(String userId, String email) async {
    try {
      final secret = _generateSecret();
      final qrCodeUrl = _generateQrCodeUrl(email, secret);
      final backupCodes = generateBackupCodes();

      final setupData = {
        'user_id': userId,
        'secret': secret,
        'qr_code_url': qrCodeUrl,
        'backup_codes': backupCodes,
        'is_enabled': false,
        'created_at': DateTime.now().toIso8601String(),
      };

      final response = await _supabaseClient
          .from('admin_mfa_setup')
          .insert(setupData)
          .select()
          .single();

      return MfaSetupModel.fromJson(response);
    } catch (e) {
      throw MfaException('MFA setup failed: ${e.toString()}');
    }
  }

  /// Verifies an MFA code (TOTP or backup code).
  ///
  /// Returns true if the code is valid, false otherwise.
  /// Throws [MfaException] if verification fails.
  Future<bool> verifyMfaCode(String userId, String code) async {
    try {
      final response = await _supabaseClient
          .from('admin_mfa_setup')
          .select('secret, backup_codes')
          .eq('user_id', userId)
          .single();

      final secret = response['secret'] as String;
      final backupCodes = (response['backup_codes'] as List<dynamic>).cast<String>();

      // First try TOTP verification
      if (validateTotpCode(secret, code)) {
        return true;
      }

      // Then try backup code verification
      if (backupCodes.contains(code)) {
        // Remove used backup code
        final updatedBackupCodes = backupCodes.where((c) => c != code).toList();
        
        await _supabaseClient
            .from('admin_mfa_setup')
            .update({'backup_codes': updatedBackupCodes})
            .eq('user_id', userId)
            .select()
            .single();

        return true;
      }

      return false;
    } catch (e) {
      throw MfaException('MFA verification failed: ${e.toString()}');
    }
  }

  /// Enables MFA for a user after successful verification.
  ///
  /// Throws [MfaException] if enabling fails or verification code is invalid.
  Future<void> enableMfa(String userId, String verificationCode) async {
    try {
      // Verify the code first
      final isValid = await verifyMfaCode(userId, verificationCode);
      
      if (!isValid) {
        throw const MfaException('Invalid verification code');
      }

      // Enable MFA
      await _supabaseClient
          .from('admin_mfa_setup')
          .update({'is_enabled': true})
          .eq('user_id', userId)
          .select()
          .single();
    } catch (e) {
      if (e is MfaException) rethrow;
      throw MfaException('Failed to enable MFA: ${e.toString()}');
    }
  }

  /// Disables MFA for a user.
  ///
  /// Throws [MfaException] if disabling fails.
  Future<void> disableMfa(String userId) async {
    try {
      await _supabaseClient
          .from('admin_mfa_setup')
          .update({'is_enabled': false})
          .eq('user_id', userId)
          .select()
          .single();
    } catch (e) {
      throw MfaException('Failed to disable MFA: ${e.toString()}');
    }
  }

  /// Gets the MFA status for a user.
  ///
  /// Returns [MfaSetupModel] if MFA is set up, null otherwise.
  Future<MfaSetupModel?> getMfaStatus(String userId) async {
    try {
      final response = await _supabaseClient
          .from('admin_mfa_setup')
          .select()
          .eq('user_id', userId)
          .single();

      return MfaSetupModel.fromJson(response);
    } catch (e) {
      // Return null if no MFA setup exists
      return null;
    }
  }

  /// Regenerates backup codes for a user.
  ///
  /// Returns the new list of backup codes.
  /// Throws [MfaException] if regeneration fails.
  Future<List<String>> regenerateBackupCodes(String userId) async {
    try {
      final newBackupCodes = generateBackupCodes();

      final response = await _supabaseClient
          .from('admin_mfa_setup')
          .update({'backup_codes': newBackupCodes})
          .eq('user_id', userId)
          .select('backup_codes')
          .single();

      return (response['backup_codes'] as List<dynamic>).cast<String>();
    } catch (e) {
      throw MfaException('Failed to regenerate backup codes: ${e.toString()}');
    }
  }

  /// Validates a TOTP code against a secret.
  ///
  /// Supports a time window tolerance for clock drift.
  bool validateTotpCode(String secret, String code) {
    final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 30000;
    
    // Check current time window and adjacent windows for clock drift tolerance
    for (int i = -1; i <= 1; i++) {
      final timeStep = currentTime + i;
      final expectedCode = generateTotpCode(secret, timeStep);
      
      if (expectedCode == code) {
        return true;
      }
    }
    
    return false;
  }

  /// Generates a TOTP code for a given secret and time step.
  String generateTotpCode(String secret, int timeStep) {
    // This is a simplified TOTP implementation
    // In production, use a proper TOTP library
    final key = base32Decode(secret);
    final timeBytes = _intToBytes(timeStep);
    
    final hmac = Hmac(sha1, key);
    final digest = hmac.convert(timeBytes);
    
    final offset = digest.bytes.last & 0x0f;
    final binary = ((digest.bytes[offset] & 0x7f) << 24) |
                   ((digest.bytes[offset + 1] & 0xff) << 16) |
                   ((digest.bytes[offset + 2] & 0xff) << 8) |
                   (digest.bytes[offset + 3] & 0xff);
    
    final otp = binary % 1000000;
    return otp.toString().padLeft(6, '0');
  }

  /// Generates backup codes for MFA.
  ///
  /// Returns a list of 5 unique 6-digit backup codes.
  List<String> generateBackupCodes() {
    final random = Random.secure();
    final codes = <String>[];
    
    while (codes.length < 5) {
      final code = random.nextInt(1000000).toString().padLeft(6, '0');
      if (!codes.contains(code)) {
        codes.add(code);
      }
    }
    
    return codes;
  }

  /// Generates a random secret for TOTP.
  String _generateSecret() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    final random = Random.secure();
    final secret = List.generate(32, (index) => chars[random.nextInt(chars.length)]);
    return secret.join();
  }

  /// Generates a QR code URL for TOTP setup.
  String _generateQrCodeUrl(String email, String secret) {
    final issuer = 'MeditatingLeo';
    final label = '$issuer:$email';
    return 'otpauth://totp/$label?secret=$secret&issuer=$issuer';
  }

  /// Converts an integer to bytes for HMAC calculation.
  List<int> _intToBytes(int value) {
    final bytes = <int>[];
    for (int i = 7; i >= 0; i--) {
      bytes.add((value >> (i * 8)) & 0xff);
    }
    return bytes;
  }

  /// Decodes a base32 string to bytes.
  List<int> base32Decode(String input) {
    // Simplified base32 decode - in production use a proper library
    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    final cleanInput = input.toUpperCase().replaceAll(RegExp(r'[^A-Z2-7]'), '');
    
    final bytes = <int>[];
    int buffer = 0;
    int bitsLeft = 0;
    
    for (int i = 0; i < cleanInput.length; i++) {
      final char = cleanInput[i];
      final value = alphabet.indexOf(char);
      
      if (value == -1) continue;
      
      buffer = (buffer << 5) | value;
      bitsLeft += 5;
      
      if (bitsLeft >= 8) {
        bytes.add((buffer >> (bitsLeft - 8)) & 0xff);
        bitsLeft -= 8;
      }
    }
    
    return bytes;
  }
}
