import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:meditatingleo_admin/features/auth/data/models/audit_log_model.dart';
import 'package:meditatingleo_admin/core/error/exceptions.dart';
import 'dart:io';

/// Service for handling security audit logging operations.
///
/// Provides comprehensive audit trail for authentication events, security incidents,
/// and admin actions for compliance and security monitoring.
class AuditLogService {
  /// Creates an [AuditLogService] instance.
  const AuditLogService(this._supabaseClient);

  final SupabaseClient _supabaseClient;

  /// Logs an authentication event.
  ///
  /// Records login attempts, MFA events, and other authentication activities.
  /// Throws [DatabaseException] if logging fails.
  Future<void> logAuthEvent({
    required String? userId,
    required AuditAction action,
    required bool success,
    required Map<String, dynamic> details,
    String? errorMessage,
  }) async {
    try {
      final logData = {
        'user_id': userId,
        'action': _actionToString(action),
        'details': {
          ...details,
          'ip_address': await _getClientIpAddress(),
          'user_agent': _getUserAgent(),
          'timestamp': DateTime.now().toIso8601String(),
        },
        'timestamp': DateTime.now().toIso8601String(),
        'success': success,
        'error_message': errorMessage,
      };

      await _supabaseClient
          .from('admin_audit_logs')
          .insert(logData);
    } catch (e) {
      // Don't throw exceptions for audit logging failures
      // as this could break the main authentication flow
      print('Audit logging failed: ${e.toString()}');
    }
  }

  /// Logs a user management event.
  ///
  /// Records user creation, role changes, account status changes, etc.
  Future<void> logUserManagementEvent({
    required String adminUserId,
    required String? targetUserId,
    required AuditAction action,
    required bool success,
    required Map<String, dynamic> details,
    String? errorMessage,
  }) async {
    try {
      final logData = {
        'user_id': adminUserId,
        'action': _actionToString(action),
        'details': {
          ...details,
          'target_user_id': targetUserId,
          'ip_address': await _getClientIpAddress(),
          'user_agent': _getUserAgent(),
          'timestamp': DateTime.now().toIso8601String(),
        },
        'timestamp': DateTime.now().toIso8601String(),
        'success': success,
        'error_message': errorMessage,
      };

      await _supabaseClient
          .from('admin_audit_logs')
          .insert(logData);
    } catch (e) {
      print('Audit logging failed: ${e.toString()}');
    }
  }

  /// Logs a system event.
  ///
  /// Records system configuration changes, security policy updates, etc.
  Future<void> logSystemEvent({
    required String adminUserId,
    required AuditAction action,
    required bool success,
    required Map<String, dynamic> details,
    String? errorMessage,
  }) async {
    try {
      final logData = {
        'user_id': adminUserId,
        'action': _actionToString(action),
        'details': {
          ...details,
          'ip_address': await _getClientIpAddress(),
          'user_agent': _getUserAgent(),
          'timestamp': DateTime.now().toIso8601String(),
        },
        'timestamp': DateTime.now().toIso8601String(),
        'success': success,
        'error_message': errorMessage,
      };

      await _supabaseClient
          .from('admin_audit_logs')
          .insert(logData);
    } catch (e) {
      print('Audit logging failed: ${e.toString()}');
    }
  }

  /// Retrieves audit logs with filtering and pagination.
  ///
  /// Returns a list of [AuditLogModel] entries matching the criteria.
  /// Throws [DatabaseException] if retrieval fails.
  Future<List<AuditLogModel>> getAuditLogs({
    String? userId,
    AuditAction? action,
    DateTime? startDate,
    DateTime? endDate,
    bool? success,
    int limit = 100,
    int offset = 0,
  }) async {
    try {
      var query = _supabaseClient
          .from('admin_audit_logs')
          .select()
          .order('timestamp', ascending: false)
          .range(offset, offset + limit - 1);

      if (userId != null) {
        query = query.eq('user_id', userId);
      }

      if (action != null) {
        query = query.eq('action', _actionToString(action));
      }

      if (startDate != null) {
        query = query.gte('timestamp', startDate.toIso8601String());
      }

      if (endDate != null) {
        query = query.lte('timestamp', endDate.toIso8601String());
      }

      if (success != null) {
        query = query.eq('success', success);
      }

      final response = await query;
      
      return (response as List<dynamic>)
          .map((json) => AuditLogModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw DatabaseException('Failed to retrieve audit logs: ${e.toString()}');
    }
  }

  /// Gets security events (failed logins, MFA failures, etc.).
  ///
  /// Returns high-priority security events for monitoring.
  Future<List<AuditLogModel>> getSecurityEvents({
    DateTime? startDate,
    DateTime? endDate,
    int limit = 50,
  }) async {
    try {
      final securityActions = [
        'login_failed',
        'mfa_failed',
        'account_locked',
        'role_changed',
      ];

      var query = _supabaseClient
          .from('admin_audit_logs')
          .select()
          .in_('action', securityActions)
          .order('timestamp', ascending: false)
          .limit(limit);

      if (startDate != null) {
        query = query.gte('timestamp', startDate.toIso8601String());
      }

      if (endDate != null) {
        query = query.lte('timestamp', endDate.toIso8601String());
      }

      final response = await query;
      
      return (response as List<dynamic>)
          .map((json) => AuditLogModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw DatabaseException('Failed to retrieve security events: ${e.toString()}');
    }
  }

  /// Gets audit statistics for a time period.
  ///
  /// Returns summary statistics for audit events.
  Future<Map<String, dynamic>> getAuditStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      var query = _supabaseClient
          .from('admin_audit_logs')
          .select('action, success');

      if (startDate != null) {
        query = query.gte('timestamp', startDate.toIso8601String());
      }

      if (endDate != null) {
        query = query.lte('timestamp', endDate.toIso8601String());
      }

      final response = await query;
      final logs = response as List<dynamic>;

      final stats = <String, dynamic>{
        'total_events': logs.length,
        'successful_events': logs.where((log) => log['success'] == true).length,
        'failed_events': logs.where((log) => log['success'] == false).length,
        'events_by_action': <String, int>{},
        'security_events': 0,
      };

      // Count events by action
      for (final log in logs) {
        final action = log['action'] as String;
        stats['events_by_action'][action] = 
            (stats['events_by_action'][action] as int? ?? 0) + 1;

        // Count security events
        if (_isSecurityAction(action)) {
          stats['security_events'] = (stats['security_events'] as int) + 1;
        }
      }

      return stats;
    } catch (e) {
      throw DatabaseException('Failed to get audit statistics: ${e.toString()}');
    }
  }

  /// Converts an [AuditAction] to its string representation.
  String _actionToString(AuditAction action) {
    switch (action) {
      case AuditAction.login:
        return 'login';
      case AuditAction.logout:
        return 'logout';
      case AuditAction.loginFailed:
        return 'login_failed';
      case AuditAction.mfaSetup:
        return 'mfa_setup';
      case AuditAction.mfaVerified:
        return 'mfa_verified';
      case AuditAction.mfaFailed:
        return 'mfa_failed';
      case AuditAction.passwordChanged:
        return 'password_changed';
      case AuditAction.roleChanged:
        return 'role_changed';
      case AuditAction.accountLocked:
        return 'account_locked';
      case AuditAction.accountUnlocked:
        return 'account_unlocked';
    }
  }

  /// Checks if an action is security-related.
  bool _isSecurityAction(String action) {
    const securityActions = [
      'login_failed',
      'mfa_failed',
      'account_locked',
      'role_changed',
      'password_changed',
    ];
    return securityActions.contains(action);
  }

  /// Gets the client IP address (simplified implementation).
  Future<String> _getClientIpAddress() async {
    try {
      // In a real implementation, this would get the actual client IP
      // For now, return a placeholder
      return 'unknown';
    } catch (e) {
      return 'unknown';
    }
  }

  /// Gets the user agent string (simplified implementation).
  String _getUserAgent() {
    try {
      // In a real implementation, this would get the actual user agent
      // For now, return a placeholder
      return 'MeditatingLeo Admin Panel';
    } catch (e) {
      return 'unknown';
    }
  }
}
