import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:meditatingleo_admin/features/auth/presentation/providers/admin_auth_provider.dart';
import 'package:meditatingleo_admin/features/auth/presentation/widgets/admin_login_form.dart';
import 'package:meditatingleo_admin/features/auth/presentation/pages/mfa_verification_page.dart';

/// Admin login page with modern Material Design 3 styling.
///
/// Provides secure authentication for admin users with role-based access control.
/// Supports MFA flow for elevated admin roles.
class AdminLoginPage extends ConsumerWidget {
  const AdminLoginPage({super.key});

  static const String routeName = '/admin/login';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(adminAuthNotifierProvider);

    // Listen for authentication state changes
    ref.listen<AsyncValue<AdminAuthState>>(
      adminAuthNotifierProvider,
      (previous, next) {
        next.when(
          data: (state) {
            if (state.requiresMfa && state.mfaToken != null) {
              // Navigate to MFA verification
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => MfaVerificationPage(
                    userId: state.user!.id,
                    mfaToken: state.mfaToken!,
                  ),
                ),
              );
            } else if (state.isAuthenticated) {
              // Navigate to main admin dashboard
              Navigator.of(context).pushReplacementNamed('/admin/dashboard');
            }
          },
          loading: () {},
          error: (error, stackTrace) {
            // Show error message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Authentication failed: ${error.toString()}'),
                backgroundColor: Theme.of(context).colorScheme.error,
                behavior: SnackBarBehavior.floating,
              ),
            );
          },
        );
      },
    );

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primaryContainer,
              Theme.of(context).colorScheme.secondaryContainer,
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 400),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Logo and title
                    _buildHeader(context),
                    
                    const SizedBox(height: 48),
                    
                    // Login form card
                    Card.filled(
                      elevation: 0,
                      child: Padding(
                        padding: const EdgeInsets.all(24.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Text(
                              'Admin Sign In',
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            
                            const SizedBox(height: 8),
                            
                            Text(
                              'Access the MeditatingLeo admin panel',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            
                            const SizedBox(height: 32),
                            
                            // Login form
                            const AdminLoginForm(),
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Security notice
                    _buildSecurityNotice(context),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        // App logo/icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            Icons.admin_panel_settings,
            size: 40,
            color: Theme.of(context).colorScheme.onPrimary,
          ),
        ),
        
        const SizedBox(height: 16),
        
        Text(
          'MeditatingLeo',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        
        Text(
          'Admin Panel',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildSecurityNotice(BuildContext context) {
    return Card.outlined(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(
              Icons.security,
              size: 20,
              color: Theme.of(context).colorScheme.primary,
            ),
            
            const SizedBox(width: 12),
            
            Expanded(
              child: Text(
                'This is a secure admin area. All activities are logged and monitored.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
