import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:meditatingleo_admin/features/auth/data/models/mfa_setup_model.dart';
import 'package:meditatingleo_admin/features/auth/data/services/mfa_service.dart';
import 'package:meditatingleo_admin/features/auth/data/services/audit_log_service.dart';
import 'package:meditatingleo_admin/features/auth/presentation/providers/admin_auth_provider.dart';
import 'package:meditatingleo_admin/core/providers/supabase_provider.dart';
import 'package:meditatingleo_admin/core/error/exceptions.dart';

part 'mfa_provider.g.dart';

/// MFA setup state
class MfaSetupState {
  const MfaSetupState({
    this.setup,
    required this.isLoading,
    this.error,
  });

  final MfaSetupModel? setup;
  final bool isLoading;
  final String? error;

  MfaSetupState copyWith({
    MfaSetupModel? setup,
    bool? isLoading,
    String? error,
  }) {
    return MfaSetupState(
      setup: setup ?? this.setup,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

/// Provider for MFA service
@riverpod
MfaService mfaService(MfaServiceRef ref) {
  final supabase = ref.watch(supabaseClientProvider);
  return MfaService(supabase);
}

/// MFA setup notifier
@riverpod
class MfaSetupNotifier extends _$MfaSetupNotifier {
  late MfaService _mfaService;
  late AuditLogService _auditLogService;

  @override
  MfaSetupState build() {
    _mfaService = ref.read(mfaServiceProvider);
    _auditLogService = ref.read(auditLogServiceProvider);
    
    return const MfaSetupState(
      setup: null,
      isLoading: false,
      error: null,
    );
  }

  /// Initiates MFA setup for the current user
  Future<void> setupMfa() async {
    final authState = ref.read(adminAuthNotifierProvider);
    final user = authState.value?.user;
    
    if (user == null) {
      state = state.copyWith(error: 'No authenticated user');
      return;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final setup = await _mfaService.setupMfa(user.id, user.email);
      
      // Log MFA setup initiation
      await _auditLogService.logAuthEvent(
        userId: user.id,
        action: AuditAction.mfaSetup,
        success: true,
        details: {
          'email': user.email,
          'setup_initiated': true,
        },
      );

      state = state.copyWith(
        setup: setup,
        isLoading: false,
        error: null,
      );
    } catch (e) {
      // Log failed MFA setup
      await _auditLogService.logAuthEvent(
        userId: user.id,
        action: AuditAction.mfaSetup,
        success: false,
        details: {
          'email': user.email,
          'error': e.toString(),
        },
        errorMessage: e.toString(),
      );

      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Verifies MFA setup with a TOTP code
  Future<bool> verifySetup(String code) async {
    final authState = ref.read(adminAuthNotifierProvider);
    final user = authState.value?.user;
    
    if (user == null) {
      state = state.copyWith(error: 'No authenticated user');
      return false;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final isValid = await _mfaService.verifyMfaCode(user.id, code);
      
      if (isValid) {
        // Enable MFA after successful verification
        await _mfaService.enableMfa(user.id, code);
        
        // Log successful MFA setup completion
        await _auditLogService.logAuthEvent(
          userId: user.id,
          action: AuditAction.mfaSetup,
          success: true,
          details: {
            'email': user.email,
            'setup_completed': true,
            'verification_method': 'totp',
          },
        );

        state = state.copyWith(
          isLoading: false,
          error: null,
        );
        
        return true;
      } else {
        // Log failed MFA verification during setup
        await _auditLogService.logAuthEvent(
          userId: user.id,
          action: AuditAction.mfaFailed,
          success: false,
          details: {
            'email': user.email,
            'verification_method': 'totp',
            'context': 'setup_verification',
          },
          errorMessage: 'Invalid verification code',
        );

        state = state.copyWith(
          isLoading: false,
          error: 'Invalid verification code',
        );
        
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      
      return false;
    }
  }

  /// Disables MFA for the current user
  Future<void> disableMfa() async {
    final authState = ref.read(adminAuthNotifierProvider);
    final user = authState.value?.user;
    
    if (user == null) {
      state = state.copyWith(error: 'No authenticated user');
      return;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      await _mfaService.disableMfa(user.id);
      
      // Log MFA disable
      await _auditLogService.logAuthEvent(
        userId: user.id,
        action: AuditAction.mfaSetup,
        success: true,
        details: {
          'email': user.email,
          'mfa_disabled': true,
        },
      );

      state = state.copyWith(
        setup: null,
        isLoading: false,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Regenerates backup codes
  Future<List<String>?> regenerateBackupCodes() async {
    final authState = ref.read(adminAuthNotifierProvider);
    final user = authState.value?.user;
    
    if (user == null) {
      state = state.copyWith(error: 'No authenticated user');
      return null;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final newCodes = await _mfaService.regenerateBackupCodes(user.id);
      
      // Log backup code regeneration
      await _auditLogService.logAuthEvent(
        userId: user.id,
        action: AuditAction.mfaSetup,
        success: true,
        details: {
          'email': user.email,
          'backup_codes_regenerated': true,
          'new_codes_count': newCodes.length,
        },
      );

      // Update the setup with new backup codes
      final currentSetup = state.setup;
      if (currentSetup != null) {
        state = state.copyWith(
          setup: currentSetup.copyWith(backupCodes: newCodes),
          isLoading: false,
          error: null,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: null,
        );
      }
      
      return newCodes;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      
      return null;
    }
  }

  /// Loads the current MFA status
  Future<void> loadMfaStatus() async {
    final authState = ref.read(adminAuthNotifierProvider);
    final user = authState.value?.user;
    
    if (user == null) {
      state = state.copyWith(error: 'No authenticated user');
      return;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final setup = await _mfaService.getMfaStatus(user.id);
      
      state = state.copyWith(
        setup: setup,
        isLoading: false,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Clears any error state
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider for MFA verification during login
@riverpod
class MfaVerificationNotifier extends _$MfaVerificationNotifier {
  late MfaService _mfaService;
  late AuditLogService _auditLogService;

  @override
  bool build() {
    _mfaService = ref.read(mfaServiceProvider);
    _auditLogService = ref.read(auditLogServiceProvider);
    
    return false; // Not verifying initially
  }

  /// Verifies MFA code during login
  Future<bool> verifyLoginMfa(String userId, String code) async {
    state = true; // Set verifying state

    try {
      final isValid = await _mfaService.verifyMfaCode(userId, code);
      
      if (isValid) {
        // Log successful MFA verification
        await _auditLogService.logAuthEvent(
          userId: userId,
          action: AuditAction.mfaVerified,
          success: true,
          details: {
            'verification_method': 'totp',
            'context': 'login',
          },
        );

        // Complete the authentication in the auth notifier
        await ref.read(adminAuthNotifierProvider.notifier).completeMfaVerification(code);
        
        state = false;
        return true;
      } else {
        // Log failed MFA verification
        await _auditLogService.logAuthEvent(
          userId: userId,
          action: AuditAction.mfaFailed,
          success: false,
          details: {
            'verification_method': 'totp',
            'context': 'login',
          },
          errorMessage: 'Invalid MFA code',
        );

        state = false;
        return false;
      }
    } catch (e) {
      state = false;
      throw MfaException('MFA verification failed: ${e.toString()}');
    }
  }
}
