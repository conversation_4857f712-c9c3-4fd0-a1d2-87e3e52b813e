// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mfa_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$mfaServiceHash() => r'1eeb79b2e89d5890b103c1effee9f3b77b05f1cd';

/// Provider for MFA service
///
/// Copied from [mfaService].
@ProviderFor(mfaService)
final mfaServiceProvider = AutoDisposeProvider<MfaService>.internal(
  mfaService,
  name: r'mfaServiceProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$mfaServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef MfaServiceRef = AutoDisposeProviderRef<MfaService>;
String _$mfaSetupNotifierHash() => r'5618e1410d2353b31cde184884542cc054ab6af3';

/// MFA setup notifier
///
/// Copied from [MfaSetupNotifier].
@ProviderFor(MfaSetupNotifier)
final mfaSetupNotifierProvider =
    AutoDisposeNotifierProvider<MfaSetupNotifier, MfaSetupState>.internal(
  MfaSetupNotifier.new,
  name: r'mfaSetupNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mfaSetupNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MfaSetupNotifier = AutoDisposeNotifier<MfaSetupState>;
String _$mfaVerificationNotifierHash() =>
    r'062a7106739a1a0aaf419f2a6a9b7f97832ff2e1';

/// Provider for MFA verification during login
///
/// Copied from [MfaVerificationNotifier].
@ProviderFor(MfaVerificationNotifier)
final mfaVerificationNotifierProvider =
    AutoDisposeNotifierProvider<MfaVerificationNotifier, bool>.internal(
  MfaVerificationNotifier.new,
  name: r'mfaVerificationNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mfaVerificationNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MfaVerificationNotifier = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
