import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:meditatingleo_admin/features/auth/data/models/admin_user_model.dart';
import 'package:meditatingleo_admin/features/auth/data/models/auth_response_model.dart';
import 'package:meditatingleo_admin/features/auth/data/services/admin_auth_service.dart';
import 'package:meditatingleo_admin/features/auth/data/services/audit_log_service.dart';
import 'package:meditatingleo_admin/features/auth/data/services/secure_storage_service.dart';
import 'package:meditatingleo_admin/core/providers/supabase_provider.dart';
import 'package:meditatingleo_admin/core/error/exceptions.dart';

part 'admin_auth_provider.g.dart';

/// Authentication state for admin users
class AdminAuthState {
  const AdminAuthState({
    this.user,
    required this.isAuthenticated,
    required this.requiresMfa,
    this.mfaToken,
  });

  final AdminUserModel? user;
  final bool isAuthenticated;
  final bool requiresMfa;
  final String? mfaToken;

  AdminAuthState copyWith({
    AdminUserModel? user,
    bool? isAuthenticated,
    bool? requiresMfa,
    String? mfaToken,
  }) {
    return AdminAuthState(
      user: user ?? this.user,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      requiresMfa: requiresMfa ?? this.requiresMfa,
      mfaToken: mfaToken ?? this.mfaToken,
    );
  }
}

/// Provider for admin authentication service
@riverpod
AdminAuthService adminAuthService(AdminAuthServiceRef ref) {
  final supabase = ref.watch(supabaseClientProvider);
  return AdminAuthService(supabase);
}

/// Provider for audit log service
@riverpod
AuditLogService auditLogService(AuditLogServiceRef ref) {
  final supabase = ref.watch(supabaseClientProvider);
  return AuditLogService(supabase);
}

/// Provider for secure storage service
@riverpod
SecureStorageService secureStorageService(SecureStorageServiceRef ref) {
  return const SecureStorageService();
}

/// Main authentication state notifier
@riverpod
class AdminAuthNotifier extends _$AdminAuthNotifier {
  late AdminAuthService _authService;
  late AuditLogService _auditLogService;
  late SecureStorageService _storageService;

  @override
  Future<AdminAuthState> build() async {
    _authService = ref.read(adminAuthServiceProvider);
    _auditLogService = ref.read(auditLogServiceProvider);
    _storageService = ref.read(secureStorageServiceProvider);

    // Check for existing authentication
    return await _checkInitialAuthState();
  }

  /// Signs in an admin user with email and password
  Future<void> signIn(String email, String password) async {
    state = const AsyncValue.loading();

    try {
      final authResponse = await _authService.signInWithEmailAndPassword(email, password);
      
      // Store tokens securely
      await _storageService.storeAuthTokens(
        accessToken: authResponse.accessToken,
        refreshToken: authResponse.refreshToken,
        expiresAt: authResponse.expiresAt,
      );

      // Store user info
      await _storageService.storeUserInfo(
        userId: authResponse.user.id,
        email: authResponse.user.email,
        role: authResponse.user.role.toString(),
        mfaEnabled: authResponse.requiresMfa,
      );

      // Log successful authentication
      await _auditLogService.logAuthEvent(
        userId: authResponse.user.id,
        action: AuditAction.login,
        success: true,
        details: {
          'email': email,
          'role': authResponse.user.role.toString(),
          'mfa_required': authResponse.requiresMfa,
        },
      );

      state = AsyncValue.data(AdminAuthState(
        user: authResponse.user,
        isAuthenticated: !authResponse.requiresMfa, // Not fully authenticated if MFA required
        requiresMfa: authResponse.requiresMfa,
        mfaToken: authResponse.mfaToken,
      ));
    } catch (e) {
      // Log failed authentication
      await _auditLogService.logAuthEvent(
        userId: null,
        action: AuditAction.loginFailed,
        success: false,
        details: {
          'email': email,
          'error': e.toString(),
        },
        errorMessage: e.toString(),
      );

      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Completes MFA verification
  Future<void> completeMfaVerification(String mfaCode) async {
    final currentState = state.value;
    if (currentState == null || !currentState.requiresMfa) {
      throw const AuthException('MFA verification not required');
    }

    try {
      // In a real implementation, this would verify the MFA code
      // For now, we'll assume verification is successful
      
      // Log successful MFA verification
      await _auditLogService.logAuthEvent(
        userId: currentState.user?.id,
        action: AuditAction.mfaVerified,
        success: true,
        details: {
          'mfa_method': 'totp',
        },
      );

      state = AsyncValue.data(currentState.copyWith(
        isAuthenticated: true,
        requiresMfa: false,
        mfaToken: null,
      ));
    } catch (e) {
      // Log failed MFA verification
      await _auditLogService.logAuthEvent(
        userId: currentState.user?.id,
        action: AuditAction.mfaFailed,
        success: false,
        details: {
          'mfa_method': 'totp',
          'error': e.toString(),
        },
        errorMessage: e.toString(),
      );

      throw AuthException('MFA verification failed: ${e.toString()}');
    }
  }

  /// Signs out the current admin user
  Future<void> signOut() async {
    final currentUser = state.value?.user;

    try {
      await _authService.signOut();
      await _storageService.clearAuthData();

      // Log successful logout
      if (currentUser != null) {
        await _auditLogService.logAuthEvent(
          userId: currentUser.id,
          action: AuditAction.logout,
          success: true,
          details: {
            'email': currentUser.email,
            'role': currentUser.role.toString(),
          },
        );
      }

      state = const AsyncValue.data(AdminAuthState(
        user: null,
        isAuthenticated: false,
        requiresMfa: false,
        mfaToken: null,
      ));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Checks the current authentication status
  Future<void> checkAuthStatus() async {
    try {
      final user = await _authService.getCurrentUser();
      
      if (user != null) {
        state = AsyncValue.data(AdminAuthState(
          user: user,
          isAuthenticated: true,
          requiresMfa: false,
          mfaToken: null,
        ));
      } else {
        state = const AsyncValue.data(AdminAuthState(
          user: null,
          isAuthenticated: false,
          requiresMfa: false,
          mfaToken: null,
        ));
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Refreshes the current session
  Future<void> refreshSession() async {
    try {
      final authResponse = await _authService.refreshSession();
      
      // Update stored tokens
      await _storageService.storeAuthTokens(
        accessToken: authResponse.accessToken,
        refreshToken: authResponse.refreshToken,
        expiresAt: authResponse.expiresAt,
      );

      final currentState = state.value;
      if (currentState != null) {
        state = AsyncValue.data(currentState.copyWith(
          user: authResponse.user,
        ));
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Changes the password for the current admin user
  Future<void> changePassword(String newPassword) async {
    final currentUser = state.value?.user;
    
    try {
      await _authService.changePassword(newPassword);

      // Log successful password change
      if (currentUser != null) {
        await _auditLogService.logAuthEvent(
          userId: currentUser.id,
          action: AuditAction.passwordChanged,
          success: true,
          details: {
            'email': currentUser.email,
          },
        );
      }
    } catch (e) {
      // Log failed password change
      if (currentUser != null) {
        await _auditLogService.logAuthEvent(
          userId: currentUser.id,
          action: AuditAction.passwordChanged,
          success: false,
          details: {
            'email': currentUser.email,
            'error': e.toString(),
          },
          errorMessage: e.toString(),
        );
      }
      
      throw AuthException('Password change failed: ${e.toString()}');
    }
  }

  /// Checks the initial authentication state
  Future<AdminAuthState> _checkInitialAuthState() async {
    try {
      // Check if we have stored authentication data
      final hasStoredAuth = await _storageService.hasStoredAuth();
      
      if (!hasStoredAuth) {
        return const AdminAuthState(
          user: null,
          isAuthenticated: false,
          requiresMfa: false,
          mfaToken: null,
        );
      }

      // Check if session is still valid
      final isSessionValid = await _storageService.isSessionValid();
      
      if (!isSessionValid) {
        await _storageService.clearAuthData();
        return const AdminAuthState(
          user: null,
          isAuthenticated: false,
          requiresMfa: false,
          mfaToken: null,
        );
      }

      // Try to get current user
      final user = await _authService.getCurrentUser();
      
      if (user != null) {
        return AdminAuthState(
          user: user,
          isAuthenticated: true,
          requiresMfa: false,
          mfaToken: null,
        );
      } else {
        await _storageService.clearAuthData();
        return const AdminAuthState(
          user: null,
          isAuthenticated: false,
          requiresMfa: false,
          mfaToken: null,
        );
      }
    } catch (e) {
      await _storageService.clearAuthData();
      return const AdminAuthState(
        user: null,
        isAuthenticated: false,
        requiresMfa: false,
        mfaToken: null,
      );
    }
  }

  /// Permission check methods
  bool get canManageContent {
    final user = state.value?.user;
    return user?.canManageContent ?? false;
  }

  bool get canManageUsers {
    final user = state.value?.user;
    return user?.canManageUsers ?? false;
  }

  bool get canManageSystem {
    final user = state.value?.user;
    return user?.canManageSystem ?? false;
  }
}
