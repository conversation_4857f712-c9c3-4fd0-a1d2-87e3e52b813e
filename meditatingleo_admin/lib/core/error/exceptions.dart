/// Base exception class for the application
abstract class AppException implements Exception {
  const AppException(this.message);
  
  final String message;
  
  @override
  String toString() => message;
}

/// Authentication-related exceptions
class AuthException extends AppException {
  const AuthException(super.message);
}

/// MFA-related exceptions
class MfaException extends AppException {
  const MfaException(super.message);
}

/// Database-related exceptions
class DatabaseException extends AppException {
  const DatabaseException(super.message);
}

/// Network-related exceptions
class NetworkException extends AppException {
  const NetworkException(super.message);
}

/// Validation-related exceptions
class ValidationException extends AppException {
  const ValidationException(super.message);
}

/// Permission-related exceptions
class PermissionException extends AppException {
  const PermissionException(super.message);
}
