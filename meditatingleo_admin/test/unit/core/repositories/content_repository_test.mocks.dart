// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in meditatingleo_admin/test/unit/core/repositories/content_repository_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i7;

import 'package:drift/drift.dart' as _i4;
import 'package:drift/src/runtime/executor/stream_queries.dart' as _i6;
import 'package:meditatingleo_admin/core/database/admin_database.dart' as _i5;
import 'package:meditatingleo_admin/core/services/supabase_service.dart' as _i8;
import 'package:meditatingleo_admin/shared/models/result.dart' as _i3;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i9;
import 'package:supabase_flutter/supabase_flutter.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeSupabaseClient_0 extends _i1.SmartFake
    implements _i2.SupabaseClient {
  _FakeSupabaseClient_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeResult_1<T, E> extends _i1.SmartFake implements _i3.Result<T, E> {
  _FakeResult_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMigrationStrategy_2 extends _i1.SmartFake
    implements _i4.MigrationStrategy {
  _FakeMigrationStrategy_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$JourneyContentTableTable_3 extends _i1.SmartFake
    implements _i5.$JourneyContentTableTable {
  _Fake$JourneyContentTableTable_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$AdminUsersTable_4 extends _i1.SmartFake
    implements _i5.$AdminUsersTable {
  _Fake$AdminUsersTable_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$SystemMetricsTable_5 extends _i1.SmartFake
    implements _i5.$SystemMetricsTable {
  _Fake$SystemMetricsTable_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$AdminDatabaseManager_6 extends _i1.SmartFake
    implements _i5.$AdminDatabaseManager {
  _Fake$AdminDatabaseManager_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGeneratedDatabase_7 extends _i1.SmartFake
    implements _i4.GeneratedDatabase {
  _FakeGeneratedDatabase_7(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDriftDatabaseOptions_8 extends _i1.SmartFake
    implements _i4.DriftDatabaseOptions {
  _FakeDriftDatabaseOptions_8(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeStreamQueryUpdateRules_9 extends _i1.SmartFake
    implements _i4.StreamQueryUpdateRules {
  _FakeStreamQueryUpdateRules_9(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDatabaseConnection_10 extends _i1.SmartFake
    implements _i4.DatabaseConnection {
  _FakeDatabaseConnection_10(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeQueryExecutor_11 extends _i1.SmartFake implements _i4.QueryExecutor {
  _FakeQueryExecutor_11(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeStreamQueryStore_12 extends _i1.SmartFake
    implements _i6.StreamQueryStore {
  _FakeStreamQueryStore_12(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDatabaseConnectionUser_13 extends _i1.SmartFake
    implements _i4.DatabaseConnectionUser {
  _FakeDatabaseConnectionUser_13(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMigrator_14 extends _i1.SmartFake implements _i4.Migrator {
  _FakeMigrator_14(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFuture_15<T1> extends _i1.SmartFake implements _i7.Future<T1> {
  _FakeFuture_15(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeInsertStatement_16<T1 extends _i4.Table, D1> extends _i1.SmartFake
    implements _i4.InsertStatement<T1, D1> {
  _FakeInsertStatement_16(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUpdateStatement_17<T extends _i4.Table, D> extends _i1.SmartFake
    implements _i4.UpdateStatement<T, D> {
  _FakeUpdateStatement_17(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSimpleSelectStatement_18<T1 extends _i4.HasResultSet, D>
    extends _i1.SmartFake implements _i4.SimpleSelectStatement<T1, D> {
  _FakeSimpleSelectStatement_18(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeJoinedSelectStatement_19<FirstT extends _i4.HasResultSet, FirstD>
    extends _i1.SmartFake implements _i4.JoinedSelectStatement<FirstT, FirstD> {
  _FakeJoinedSelectStatement_19(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBaseSelectStatement_20<Row> extends _i1.SmartFake
    implements _i4.BaseSelectStatement<Row> {
  _FakeBaseSelectStatement_20(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDeleteStatement_21<T1 extends _i4.Table, D1> extends _i1.SmartFake
    implements _i4.DeleteStatement<T1, D1> {
  _FakeDeleteStatement_21(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSelectable_22<T> extends _i1.SmartFake implements _i4.Selectable<T> {
  _FakeSelectable_22(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGenerationContext_23 extends _i1.SmartFake
    implements _i4.GenerationContext {
  _FakeGenerationContext_23(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [SupabaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockSupabaseService extends _i1.Mock implements _i8.SupabaseService {
  MockSupabaseService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.SupabaseClient get client => (super.noSuchMethod(
        Invocation.getter(#client),
        returnValue: _FakeSupabaseClient_0(
          this,
          Invocation.getter(#client),
        ),
      ) as _i2.SupabaseClient);

  @override
  _i7.Stream<_i2.AuthState> get authStateStream => (super.noSuchMethod(
        Invocation.getter(#authStateStream),
        returnValue: _i7.Stream<_i2.AuthState>.empty(),
      ) as _i7.Stream<_i2.AuthState>);

  @override
  _i7.Future<_i3.Result<_i2.AuthResponse, _i3.AppError>> signInWithEmail(
    String? email,
    String? password,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithEmail,
          [
            email,
            password,
          ],
        ),
        returnValue:
            _i7.Future<_i3.Result<_i2.AuthResponse, _i3.AppError>>.value(
                _FakeResult_1<_i2.AuthResponse, _i3.AppError>(
          this,
          Invocation.method(
            #signInWithEmail,
            [
              email,
              password,
            ],
          ),
        )),
      ) as _i7.Future<_i3.Result<_i2.AuthResponse, _i3.AppError>>);

  @override
  _i7.Future<_i3.Result<void, _i3.AppError>> signOut() => (super.noSuchMethod(
        Invocation.method(
          #signOut,
          [],
        ),
        returnValue: _i7.Future<_i3.Result<void, _i3.AppError>>.value(
            _FakeResult_1<void, _i3.AppError>(
          this,
          Invocation.method(
            #signOut,
            [],
          ),
        )),
      ) as _i7.Future<_i3.Result<void, _i3.AppError>>);

  @override
  _i7.Future<_i3.Result<List<Map<String, dynamic>>, _i3.AppError>> query(
          String? tableName) =>
      (super.noSuchMethod(
        Invocation.method(
          #query,
          [tableName],
        ),
        returnValue: _i7
            .Future<_i3.Result<List<Map<String, dynamic>>, _i3.AppError>>.value(
            _FakeResult_1<List<Map<String, dynamic>>, _i3.AppError>(
          this,
          Invocation.method(
            #query,
            [tableName],
          ),
        )),
      ) as _i7.Future<_i3.Result<List<Map<String, dynamic>>, _i3.AppError>>);

  @override
  _i7.Future<_i3.Result<List<Map<String, dynamic>>, _i3.AppError>> queryById(
    String? tableName,
    String? id,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #queryById,
          [
            tableName,
            id,
          ],
        ),
        returnValue: _i7
            .Future<_i3.Result<List<Map<String, dynamic>>, _i3.AppError>>.value(
            _FakeResult_1<List<Map<String, dynamic>>, _i3.AppError>(
          this,
          Invocation.method(
            #queryById,
            [
              tableName,
              id,
            ],
          ),
        )),
      ) as _i7.Future<_i3.Result<List<Map<String, dynamic>>, _i3.AppError>>);

  @override
  _i7.Future<_i3.Result<List<Map<String, dynamic>>, _i3.AppError>> insert(
    String? tableName,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #insert,
          [
            tableName,
            data,
          ],
        ),
        returnValue: _i7
            .Future<_i3.Result<List<Map<String, dynamic>>, _i3.AppError>>.value(
            _FakeResult_1<List<Map<String, dynamic>>, _i3.AppError>(
          this,
          Invocation.method(
            #insert,
            [
              tableName,
              data,
            ],
          ),
        )),
      ) as _i7.Future<_i3.Result<List<Map<String, dynamic>>, _i3.AppError>>);

  @override
  _i7.Future<_i3.Result<List<Map<String, dynamic>>, _i3.AppError>> update(
    String? tableName,
    String? id,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #update,
          [
            tableName,
            id,
            data,
          ],
        ),
        returnValue: _i7
            .Future<_i3.Result<List<Map<String, dynamic>>, _i3.AppError>>.value(
            _FakeResult_1<List<Map<String, dynamic>>, _i3.AppError>(
          this,
          Invocation.method(
            #update,
            [
              tableName,
              id,
              data,
            ],
          ),
        )),
      ) as _i7.Future<_i3.Result<List<Map<String, dynamic>>, _i3.AppError>>);

  @override
  _i7.Future<_i3.Result<void, _i3.AppError>> delete(
    String? tableName,
    String? id,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [
            tableName,
            id,
          ],
        ),
        returnValue: _i7.Future<_i3.Result<void, _i3.AppError>>.value(
            _FakeResult_1<void, _i3.AppError>(
          this,
          Invocation.method(
            #delete,
            [
              tableName,
              id,
            ],
          ),
        )),
      ) as _i7.Future<_i3.Result<void, _i3.AppError>>);

  @override
  _i7.Future<_i3.Result<dynamic, _i3.AppError>> rpc(
    String? functionName,
    Map<String, dynamic>? params,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #rpc,
          [
            functionName,
            params,
          ],
        ),
        returnValue: _i7.Future<_i3.Result<dynamic, _i3.AppError>>.value(
            _FakeResult_1<dynamic, _i3.AppError>(
          this,
          Invocation.method(
            #rpc,
            [
              functionName,
              params,
            ],
          ),
        )),
      ) as _i7.Future<_i3.Result<dynamic, _i3.AppError>>);

  @override
  _i7.Stream<Map<String, dynamic>> subscribe(String? tableName) =>
      (super.noSuchMethod(
        Invocation.method(
          #subscribe,
          [tableName],
        ),
        returnValue: _i7.Stream<Map<String, dynamic>>.empty(),
      ) as _i7.Stream<Map<String, dynamic>>);

  @override
  bool hasAdminPermissions() => (super.noSuchMethod(
        Invocation.method(
          #hasAdminPermissions,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  List<String> getUserPermissions() => (super.noSuchMethod(
        Invocation.method(
          #getUserPermissions,
          [],
        ),
        returnValue: <String>[],
      ) as List<String>);
}

/// A class which mocks [AdminDatabase].
///
/// See the documentation for Mockito's code generation for more information.
class MockAdminDatabase extends _i1.Mock implements _i5.AdminDatabase {
  MockAdminDatabase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  int get schemaVersion => (super.noSuchMethod(
        Invocation.getter(#schemaVersion),
        returnValue: 0,
      ) as int);

  @override
  _i4.MigrationStrategy get migration => (super.noSuchMethod(
        Invocation.getter(#migration),
        returnValue: _FakeMigrationStrategy_2(
          this,
          Invocation.getter(#migration),
        ),
      ) as _i4.MigrationStrategy);

  @override
  _i5.$JourneyContentTableTable get journeyContentTable => (super.noSuchMethod(
        Invocation.getter(#journeyContentTable),
        returnValue: _Fake$JourneyContentTableTable_3(
          this,
          Invocation.getter(#journeyContentTable),
        ),
      ) as _i5.$JourneyContentTableTable);

  @override
  _i5.$AdminUsersTable get adminUsers => (super.noSuchMethod(
        Invocation.getter(#adminUsers),
        returnValue: _Fake$AdminUsersTable_4(
          this,
          Invocation.getter(#adminUsers),
        ),
      ) as _i5.$AdminUsersTable);

  @override
  _i5.$SystemMetricsTable get systemMetrics => (super.noSuchMethod(
        Invocation.getter(#systemMetrics),
        returnValue: _Fake$SystemMetricsTable_5(
          this,
          Invocation.getter(#systemMetrics),
        ),
      ) as _i5.$SystemMetricsTable);

  @override
  _i5.$AdminDatabaseManager get managers => (super.noSuchMethod(
        Invocation.getter(#managers),
        returnValue: _Fake$AdminDatabaseManager_6(
          this,
          Invocation.getter(#managers),
        ),
      ) as _i5.$AdminDatabaseManager);

  @override
  Iterable<_i4.TableInfo<_i4.Table, Object?>> get allTables =>
      (super.noSuchMethod(
        Invocation.getter(#allTables),
        returnValue: <_i4.TableInfo<_i4.Table, Object?>>[],
      ) as Iterable<_i4.TableInfo<_i4.Table, Object?>>);

  @override
  List<_i4.DatabaseSchemaEntity> get allSchemaEntities => (super.noSuchMethod(
        Invocation.getter(#allSchemaEntities),
        returnValue: <_i4.DatabaseSchemaEntity>[],
      ) as List<_i4.DatabaseSchemaEntity>);

  @override
  _i4.GeneratedDatabase get attachedDatabase => (super.noSuchMethod(
        Invocation.getter(#attachedDatabase),
        returnValue: _FakeGeneratedDatabase_7(
          this,
          Invocation.getter(#attachedDatabase),
        ),
      ) as _i4.GeneratedDatabase);

  @override
  _i4.DriftDatabaseOptions get options => (super.noSuchMethod(
        Invocation.getter(#options),
        returnValue: _FakeDriftDatabaseOptions_8(
          this,
          Invocation.getter(#options),
        ),
      ) as _i4.DriftDatabaseOptions);

  @override
  _i4.StreamQueryUpdateRules get streamUpdateRules => (super.noSuchMethod(
        Invocation.getter(#streamUpdateRules),
        returnValue: _FakeStreamQueryUpdateRules_9(
          this,
          Invocation.getter(#streamUpdateRules),
        ),
      ) as _i4.StreamQueryUpdateRules);

  @override
  _i4.DatabaseConnection get connection => (super.noSuchMethod(
        Invocation.getter(#connection),
        returnValue: _FakeDatabaseConnection_10(
          this,
          Invocation.getter(#connection),
        ),
      ) as _i4.DatabaseConnection);

  @override
  _i4.SqlTypes get typeMapping => (super.noSuchMethod(
        Invocation.getter(#typeMapping),
        returnValue: _i9.dummyValue<_i4.SqlTypes>(
          this,
          Invocation.getter(#typeMapping),
        ),
      ) as _i4.SqlTypes);

  @override
  _i4.QueryExecutor get executor => (super.noSuchMethod(
        Invocation.getter(#executor),
        returnValue: _FakeQueryExecutor_11(
          this,
          Invocation.getter(#executor),
        ),
      ) as _i4.QueryExecutor);

  @override
  _i6.StreamQueryStore get streamQueries => (super.noSuchMethod(
        Invocation.getter(#streamQueries),
        returnValue: _FakeStreamQueryStore_12(
          this,
          Invocation.getter(#streamQueries),
        ),
      ) as _i6.StreamQueryStore);

  @override
  _i4.DatabaseConnectionUser get resolvedEngine => (super.noSuchMethod(
        Invocation.getter(#resolvedEngine),
        returnValue: _FakeDatabaseConnectionUser_13(
          this,
          Invocation.getter(#resolvedEngine),
        ),
      ) as _i4.DatabaseConnectionUser);

  @override
  _i7.Future<int> insertJourneyContent(
          _i5.JourneyContentDataCompanion? journey) =>
      (super.noSuchMethod(
        Invocation.method(
          #insertJourneyContent,
          [journey],
        ),
        returnValue: _i7.Future<int>.value(0),
      ) as _i7.Future<int>);

  @override
  _i7.Future<List<_i5.JourneyContentData>> getAllJourneyContent() =>
      (super.noSuchMethod(
        Invocation.method(
          #getAllJourneyContent,
          [],
        ),
        returnValue: _i7.Future<List<_i5.JourneyContentData>>.value(
            <_i5.JourneyContentData>[]),
      ) as _i7.Future<List<_i5.JourneyContentData>>);

  @override
  _i7.Future<_i5.JourneyContentData?> getJourneyContentById(String? id) =>
      (super.noSuchMethod(
        Invocation.method(
          #getJourneyContentById,
          [id],
        ),
        returnValue: _i7.Future<_i5.JourneyContentData?>.value(),
      ) as _i7.Future<_i5.JourneyContentData?>);

  @override
  _i7.Future<bool> updateJourneyContent(
    String? id,
    _i5.JourneyContentDataCompanion? journey,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateJourneyContent,
          [
            id,
            journey,
          ],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<bool> deleteJourneyContent(String? id) => (super.noSuchMethod(
        Invocation.method(
          #deleteJourneyContent,
          [id],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<int> insertAdminUser(_i5.AdminUserDataCompanion? user) =>
      (super.noSuchMethod(
        Invocation.method(
          #insertAdminUser,
          [user],
        ),
        returnValue: _i7.Future<int>.value(0),
      ) as _i7.Future<int>);

  @override
  _i7.Future<List<_i5.AdminUserData>> getAllAdminUsers() => (super.noSuchMethod(
        Invocation.method(
          #getAllAdminUsers,
          [],
        ),
        returnValue:
            _i7.Future<List<_i5.AdminUserData>>.value(<_i5.AdminUserData>[]),
      ) as _i7.Future<List<_i5.AdminUserData>>);

  @override
  _i7.Future<_i5.AdminUserData?> getAdminUserById(String? id) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAdminUserById,
          [id],
        ),
        returnValue: _i7.Future<_i5.AdminUserData?>.value(),
      ) as _i7.Future<_i5.AdminUserData?>);

  @override
  _i7.Future<int> insertSystemMetrics(
          _i5.SystemMetricsDataCompanion? metrics) =>
      (super.noSuchMethod(
        Invocation.method(
          #insertSystemMetrics,
          [metrics],
        ),
        returnValue: _i7.Future<int>.value(0),
      ) as _i7.Future<int>);

  @override
  _i7.Future<List<_i5.SystemMetricsData>> getAllSystemMetrics() =>
      (super.noSuchMethod(
        Invocation.method(
          #getAllSystemMetrics,
          [],
        ),
        returnValue: _i7.Future<List<_i5.SystemMetricsData>>.value(
            <_i5.SystemMetricsData>[]),
      ) as _i7.Future<List<_i5.SystemMetricsData>>);

  @override
  _i7.Future<List<_i5.SystemMetricsData>> getSystemMetricsByType(
          String? metricType) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSystemMetricsByType,
          [metricType],
        ),
        returnValue: _i7.Future<List<_i5.SystemMetricsData>>.value(
            <_i5.SystemMetricsData>[]),
      ) as _i7.Future<List<_i5.SystemMetricsData>>);

  @override
  _i4.Migrator createMigrator() => (super.noSuchMethod(
        Invocation.method(
          #createMigrator,
          [],
        ),
        returnValue: _FakeMigrator_14(
          this,
          Invocation.method(
            #createMigrator,
            [],
          ),
        ),
      ) as _i4.Migrator);

  @override
  _i7.Future<void> beforeOpen(
    _i4.QueryExecutor? executor,
    _i4.OpeningDetails? details,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #beforeOpen,
          [
            executor,
            details,
          ],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Stream<T> createStream<T extends Object>(
          _i6.QueryStreamFetcher<T>? stmt) =>
      (super.noSuchMethod(
        Invocation.method(
          #createStream,
          [stmt],
        ),
        returnValue: _i7.Stream<T>.empty(),
      ) as _i7.Stream<T>);

  @override
  T alias<T, D>(
    _i4.ResultSetImplementation<T, D>? table,
    String? alias,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #alias,
          [
            table,
            alias,
          ],
        ),
        returnValue: _i9.dummyValue<T>(
          this,
          Invocation.method(
            #alias,
            [
              table,
              alias,
            ],
          ),
        ),
      ) as T);

  @override
  void markTablesUpdated(Iterable<_i4.TableInfo<_i4.Table, dynamic>>? tables) =>
      super.noSuchMethod(
        Invocation.method(
          #markTablesUpdated,
          [tables],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyUpdates(Set<_i4.TableUpdate>? updates) => super.noSuchMethod(
        Invocation.method(
          #notifyUpdates,
          [updates],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i7.Stream<Set<_i4.TableUpdate>> tableUpdates(
          [_i4.TableUpdateQuery? query = const _i4.TableUpdateQuery.any()]) =>
      (super.noSuchMethod(
        Invocation.method(
          #tableUpdates,
          [query],
        ),
        returnValue: _i7.Stream<Set<_i4.TableUpdate>>.empty(),
      ) as _i7.Stream<Set<_i4.TableUpdate>>);

  @override
  _i7.Future<T> doWhenOpened<T>(
          _i7.FutureOr<T> Function(_i4.QueryExecutor)? fn) =>
      (super.noSuchMethod(
        Invocation.method(
          #doWhenOpened,
          [fn],
        ),
        returnValue: _i9.ifNotNull(
              _i9.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #doWhenOpened,
                  [fn],
                ),
              ),
              (T v) => _i7.Future<T>.value(v),
            ) ??
            _FakeFuture_15<T>(
              this,
              Invocation.method(
                #doWhenOpened,
                [fn],
              ),
            ),
      ) as _i7.Future<T>);

  @override
  _i4.InsertStatement<T, D> into<T extends _i4.Table, D>(
          _i4.TableInfo<T, D>? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #into,
          [table],
        ),
        returnValue: _FakeInsertStatement_16<T, D>(
          this,
          Invocation.method(
            #into,
            [table],
          ),
        ),
      ) as _i4.InsertStatement<T, D>);

  @override
  _i4.UpdateStatement<Tbl, R> update<Tbl extends _i4.Table, R>(
          _i4.TableInfo<Tbl, R>? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #update,
          [table],
        ),
        returnValue: _FakeUpdateStatement_17<Tbl, R>(
          this,
          Invocation.method(
            #update,
            [table],
          ),
        ),
      ) as _i4.UpdateStatement<Tbl, R>);

  @override
  _i4.SimpleSelectStatement<T, R> select<T extends _i4.HasResultSet, R>(
    _i4.ResultSetImplementation<T, R>? table, {
    bool? distinct = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #select,
          [table],
          {#distinct: distinct},
        ),
        returnValue: _FakeSimpleSelectStatement_18<T, R>(
          this,
          Invocation.method(
            #select,
            [table],
            {#distinct: distinct},
          ),
        ),
      ) as _i4.SimpleSelectStatement<T, R>);

  @override
  _i4.JoinedSelectStatement<T, R> selectOnly<T extends _i4.HasResultSet, R>(
    _i4.ResultSetImplementation<T, R>? table, {
    bool? distinct = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #selectOnly,
          [table],
          {#distinct: distinct},
        ),
        returnValue: _FakeJoinedSelectStatement_19<T, R>(
          this,
          Invocation.method(
            #selectOnly,
            [table],
            {#distinct: distinct},
          ),
        ),
      ) as _i4.JoinedSelectStatement<T, R>);

  @override
  _i4.BaseSelectStatement<_i4.TypedResult> selectExpressions(
          Iterable<_i4.Expression<Object>>? columns) =>
      (super.noSuchMethod(
        Invocation.method(
          #selectExpressions,
          [columns],
        ),
        returnValue: _FakeBaseSelectStatement_20<_i4.TypedResult>(
          this,
          Invocation.method(
            #selectExpressions,
            [columns],
          ),
        ),
      ) as _i4.BaseSelectStatement<_i4.TypedResult>);

  @override
  _i4.DeleteStatement<T, D> delete<T extends _i4.Table, D>(
          _i4.TableInfo<T, D>? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [table],
        ),
        returnValue: _FakeDeleteStatement_21<T, D>(
          this,
          Invocation.method(
            #delete,
            [table],
          ),
        ),
      ) as _i4.DeleteStatement<T, D>);

  @override
  _i7.Future<int> customUpdate(
    String? query, {
    List<_i4.Variable<Object>>? variables = const [],
    Set<_i4.ResultSetImplementation<dynamic, dynamic>>? updates,
    _i4.UpdateKind? updateKind,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customUpdate,
          [query],
          {
            #variables: variables,
            #updates: updates,
            #updateKind: updateKind,
          },
        ),
        returnValue: _i7.Future<int>.value(0),
      ) as _i7.Future<int>);

  @override
  _i7.Future<int> customInsert(
    String? query, {
    List<_i4.Variable<Object>>? variables = const [],
    Set<_i4.ResultSetImplementation<dynamic, dynamic>>? updates,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customInsert,
          [query],
          {
            #variables: variables,
            #updates: updates,
          },
        ),
        returnValue: _i7.Future<int>.value(0),
      ) as _i7.Future<int>);

  @override
  _i7.Future<List<_i4.QueryRow>> customWriteReturning(
    String? query, {
    List<_i4.Variable<Object>>? variables = const [],
    Set<_i4.ResultSetImplementation<dynamic, dynamic>>? updates,
    _i4.UpdateKind? updateKind,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customWriteReturning,
          [query],
          {
            #variables: variables,
            #updates: updates,
            #updateKind: updateKind,
          },
        ),
        returnValue: _i7.Future<List<_i4.QueryRow>>.value(<_i4.QueryRow>[]),
      ) as _i7.Future<List<_i4.QueryRow>>);

  @override
  _i4.Selectable<_i4.QueryRow> customSelect(
    String? query, {
    List<_i4.Variable<Object>>? variables = const [],
    Set<_i4.ResultSetImplementation<dynamic, dynamic>>? readsFrom = const {},
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customSelect,
          [query],
          {
            #variables: variables,
            #readsFrom: readsFrom,
          },
        ),
        returnValue: _FakeSelectable_22<_i4.QueryRow>(
          this,
          Invocation.method(
            #customSelect,
            [query],
            {
              #variables: variables,
              #readsFrom: readsFrom,
            },
          ),
        ),
      ) as _i4.Selectable<_i4.QueryRow>);

  @override
  _i4.Selectable<_i4.QueryRow> customSelectQuery(
    String? query, {
    List<_i4.Variable<Object>>? variables = const [],
    Set<_i4.ResultSetImplementation<dynamic, dynamic>>? readsFrom = const {},
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customSelectQuery,
          [query],
          {
            #variables: variables,
            #readsFrom: readsFrom,
          },
        ),
        returnValue: _FakeSelectable_22<_i4.QueryRow>(
          this,
          Invocation.method(
            #customSelectQuery,
            [query],
            {
              #variables: variables,
              #readsFrom: readsFrom,
            },
          ),
        ),
      ) as _i4.Selectable<_i4.QueryRow>);

  @override
  _i7.Future<void> customStatement(
    String? statement, [
    List<dynamic>? args,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #customStatement,
          [
            statement,
            args,
          ],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<T> transaction<T>(
    _i7.Future<T> Function()? action, {
    bool? requireNew = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #transaction,
          [action],
          {#requireNew: requireNew},
        ),
        returnValue: _i9.ifNotNull(
              _i9.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #transaction,
                  [action],
                  {#requireNew: requireNew},
                ),
              ),
              (T v) => _i7.Future<T>.value(v),
            ) ??
            _FakeFuture_15<T>(
              this,
              Invocation.method(
                #transaction,
                [action],
                {#requireNew: requireNew},
              ),
            ),
      ) as _i7.Future<T>);

  @override
  _i7.Future<T> exclusively<T>(_i7.Future<T> Function()? action) =>
      (super.noSuchMethod(
        Invocation.method(
          #exclusively,
          [action],
        ),
        returnValue: _i9.ifNotNull(
              _i9.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #exclusively,
                  [action],
                ),
              ),
              (T v) => _i7.Future<T>.value(v),
            ) ??
            _FakeFuture_15<T>(
              this,
              Invocation.method(
                #exclusively,
                [action],
              ),
            ),
      ) as _i7.Future<T>);

  @override
  _i7.Future<void> batch(_i7.FutureOr<void> Function(_i4.Batch)? runInBatch) =>
      (super.noSuchMethod(
        Invocation.method(
          #batch,
          [runInBatch],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<T> runWithInterceptor<T>(
    _i7.Future<T> Function()? action, {
    required _i4.QueryInterceptor? interceptor,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #runWithInterceptor,
          [action],
          {#interceptor: interceptor},
        ),
        returnValue: _i9.ifNotNull(
              _i9.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #runWithInterceptor,
                  [action],
                  {#interceptor: interceptor},
                ),
              ),
              (T v) => _i7.Future<T>.value(v),
            ) ??
            _FakeFuture_15<T>(
              this,
              Invocation.method(
                #runWithInterceptor,
                [action],
                {#interceptor: interceptor},
              ),
            ),
      ) as _i7.Future<T>);

  @override
  _i4.GenerationContext $write(
    _i4.Component? component, {
    bool? hasMultipleTables,
    int? startIndex,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #$write,
          [component],
          {
            #hasMultipleTables: hasMultipleTables,
            #startIndex: startIndex,
          },
        ),
        returnValue: _FakeGenerationContext_23(
          this,
          Invocation.method(
            #$write,
            [component],
            {
              #hasMultipleTables: hasMultipleTables,
              #startIndex: startIndex,
            },
          ),
        ),
      ) as _i4.GenerationContext);

  @override
  _i4.GenerationContext $writeInsertable(
    _i4.TableInfo<_i4.Table, dynamic>? table,
    _i4.Insertable<dynamic>? insertable, {
    int? startIndex,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #$writeInsertable,
          [
            table,
            insertable,
          ],
          {#startIndex: startIndex},
        ),
        returnValue: _FakeGenerationContext_23(
          this,
          Invocation.method(
            #$writeInsertable,
            [
              table,
              insertable,
            ],
            {#startIndex: startIndex},
          ),
        ),
      ) as _i4.GenerationContext);

  @override
  String $expandVar(
    int? start,
    int? amount,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #$expandVar,
          [
            start,
            amount,
          ],
        ),
        returnValue: _i9.dummyValue<String>(
          this,
          Invocation.method(
            #$expandVar,
            [
              start,
              amount,
            ],
          ),
        ),
      ) as String);
}
