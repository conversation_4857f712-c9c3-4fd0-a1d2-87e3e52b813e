import 'package:flutter_test/flutter_test.dart';
import 'package:meditatingleo_admin/features/auth/data/models/admin_user_model.dart';

void main() {
  group('AdminUserModel', () {
    const testAdminUser = AdminUserModel(
      id: 'test-id',
      email: '<EMAIL>',
      role: AdminRole.contentManager,
      isActive: true,
      lastLoginAt: null,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    );

    group('fromJson', () {
      test('should create AdminUserModel from valid JSON', () {
        // Arrange
        final json = {
          'id': 'test-id',
          'email': '<EMAIL>',
          'role': 'content_manager',
          'is_active': true,
          'last_login_at': null,
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T00:00:00Z',
        };

        // Act
        final result = AdminUserModel.fromJson(json);

        // Assert
        expect(result.id, 'test-id');
        expect(result.email, '<EMAIL>');
        expect(result.role, AdminRole.contentManager);
        expect(result.isActive, true);
        expect(result.lastLoginAt, null);
        expect(result.createdAt, '2024-01-01T00:00:00Z');
        expect(result.updatedAt, '2024-01-01T00:00:00Z');
      });

      test('should handle system_admin role correctly', () {
        // Arrange
        final json = {
          'id': 'test-id',
          'email': '<EMAIL>',
          'role': 'system_admin',
          'is_active': true,
          'last_login_at': '2024-01-01T12:00:00Z',
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T00:00:00Z',
        };

        // Act
        final result = AdminUserModel.fromJson(json);

        // Assert
        expect(result.role, AdminRole.systemAdmin);
        expect(result.lastLoginAt, '2024-01-01T12:00:00Z');
      });

      test('should handle super_admin role correctly', () {
        // Arrange
        final json = {
          'id': 'test-id',
          'email': '<EMAIL>',
          'role': 'super_admin',
          'is_active': true,
          'last_login_at': null,
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T00:00:00Z',
        };

        // Act
        final result = AdminUserModel.fromJson(json);

        // Assert
        expect(result.role, AdminRole.superAdmin);
      });
    });

    group('toJson', () {
      test('should convert AdminUserModel to JSON correctly', () {
        // Act
        final result = testAdminUser.toJson();

        // Assert
        expect(result['id'], 'test-id');
        expect(result['email'], '<EMAIL>');
        expect(result['role'], 'content_manager');
        expect(result['is_active'], true);
        expect(result['last_login_at'], null);
        expect(result['created_at'], '2024-01-01T00:00:00Z');
        expect(result['updated_at'], '2024-01-01T00:00:00Z');
      });

      test('should convert system admin to JSON correctly', () {
        // Arrange
        const systemAdmin = AdminUserModel(
          id: 'test-id',
          email: '<EMAIL>',
          role: AdminRole.systemAdmin,
          isActive: true,
          lastLoginAt: '2024-01-01T12:00:00Z',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        );

        // Act
        final result = systemAdmin.toJson();

        // Assert
        expect(result['role'], 'system_admin');
        expect(result['last_login_at'], '2024-01-01T12:00:00Z');
      });
    });

    group('copyWith', () {
      test('should create copy with updated fields', () {
        // Act
        final result = testAdminUser.copyWith(
          email: '<EMAIL>',
          role: AdminRole.systemAdmin,
          isActive: false,
        );

        // Assert
        expect(result.id, testAdminUser.id);
        expect(result.email, '<EMAIL>');
        expect(result.role, AdminRole.systemAdmin);
        expect(result.isActive, false);
        expect(result.createdAt, testAdminUser.createdAt);
        expect(result.updatedAt, testAdminUser.updatedAt);
      });

      test('should preserve original values when no changes', () {
        // Act
        final result = testAdminUser.copyWith();

        // Assert
        expect(result, testAdminUser);
      });
    });

    group('equality', () {
      test('should be equal when all properties match', () {
        // Arrange
        const other = AdminUserModel(
          id: 'test-id',
          email: '<EMAIL>',
          role: AdminRole.contentManager,
          isActive: true,
          lastLoginAt: null,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        );

        // Assert
        expect(testAdminUser, other);
        expect(testAdminUser.hashCode, other.hashCode);
      });

      test('should not be equal when properties differ', () {
        // Arrange
        final other = testAdminUser.copyWith(email: '<EMAIL>');

        // Assert
        expect(testAdminUser, isNot(other));
        expect(testAdminUser.hashCode, isNot(other.hashCode));
      });
    });

    group('role permissions', () {
      test('should identify content manager permissions correctly', () {
        // Arrange
        const contentManager = AdminUserModel(
          id: 'test-id',
          email: '<EMAIL>',
          role: AdminRole.contentManager,
          isActive: true,
          lastLoginAt: null,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        );

        // Assert
        expect(contentManager.canManageContent, true);
        expect(contentManager.canManageUsers, false);
        expect(contentManager.canManageSystem, false);
      });

      test('should identify system admin permissions correctly', () {
        // Arrange
        const systemAdmin = AdminUserModel(
          id: 'test-id',
          email: '<EMAIL>',
          role: AdminRole.systemAdmin,
          isActive: true,
          lastLoginAt: null,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        );

        // Assert
        expect(systemAdmin.canManageContent, true);
        expect(systemAdmin.canManageUsers, true);
        expect(systemAdmin.canManageSystem, false);
      });

      test('should identify super admin permissions correctly', () {
        // Arrange
        const superAdmin = AdminUserModel(
          id: 'test-id',
          email: '<EMAIL>',
          role: AdminRole.superAdmin,
          isActive: true,
          lastLoginAt: null,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        );

        // Assert
        expect(superAdmin.canManageContent, true);
        expect(superAdmin.canManageUsers, true);
        expect(superAdmin.canManageSystem, true);
      });
    });
  });
}
