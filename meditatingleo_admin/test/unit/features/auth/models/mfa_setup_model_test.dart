import 'package:flutter_test/flutter_test.dart';
import 'package:meditatingleo_admin/features/auth/data/models/mfa_setup_model.dart';

void main() {
  group('MfaSetupModel', () {
    const testMfaSetup = MfaSetupModel(
      secret: 'JBSWY3DPEHPK3PXP',
      qrCodeUrl: 'otpauth://totp/MeditatingLeo:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=MeditatingLeo',
      backupCodes: ['123456', '789012', '345678'],
      isEnabled: false,
    );

    group('fromJson', () {
      test('should create MfaSetupModel from valid JSON', () {
        // Arrange
        final json = {
          'secret': 'JBSWY3DPEHPK3PXP',
          'qr_code_url': 'otpauth://totp/MeditatingLeo:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=MeditatingLeo',
          'backup_codes': ['123456', '789012', '345678'],
          'is_enabled': false,
        };

        // Act
        final result = MfaSetupModel.fromJson(json);

        // Assert
        expect(result.secret, 'JBSWY3DPEHPK3PXP');
        expect(result.qrCodeUrl, 'otpauth://totp/MeditatingLeo:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=MeditatingLeo');
        expect(result.backupCodes, ['123456', '789012', '345678']);
        expect(result.isEnabled, false);
      });

      test('should handle enabled MFA setup correctly', () {
        // Arrange
        final json = {
          'secret': 'JBSWY3DPEHPK3PXP',
          'qr_code_url': 'otpauth://totp/MeditatingLeo:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=MeditatingLeo',
          'backup_codes': ['123456', '789012'],
          'is_enabled': true,
        };

        // Act
        final result = MfaSetupModel.fromJson(json);

        // Assert
        expect(result.isEnabled, true);
        expect(result.backupCodes.length, 2);
      });

      test('should handle empty backup codes', () {
        // Arrange
        final json = {
          'secret': 'JBSWY3DPEHPK3PXP',
          'qr_code_url': 'otpauth://totp/MeditatingLeo:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=MeditatingLeo',
          'backup_codes': <String>[],
          'is_enabled': false,
        };

        // Act
        final result = MfaSetupModel.fromJson(json);

        // Assert
        expect(result.backupCodes, isEmpty);
      });
    });

    group('toJson', () {
      test('should convert MfaSetupModel to JSON correctly', () {
        // Act
        final result = testMfaSetup.toJson();

        // Assert
        expect(result['secret'], 'JBSWY3DPEHPK3PXP');
        expect(result['qr_code_url'], 'otpauth://totp/MeditatingLeo:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=MeditatingLeo');
        expect(result['backup_codes'], ['123456', '789012', '345678']);
        expect(result['is_enabled'], false);
      });

      test('should convert enabled MFA setup to JSON correctly', () {
        // Arrange
        final enabledMfa = testMfaSetup.copyWith(isEnabled: true);

        // Act
        final result = enabledMfa.toJson();

        // Assert
        expect(result['is_enabled'], true);
      });
    });

    group('copyWith', () {
      test('should create copy with updated fields', () {
        // Act
        final result = testMfaSetup.copyWith(
          secret: 'NEWSECRET123',
          isEnabled: true,
          backupCodes: ['111111', '222222'],
        );

        // Assert
        expect(result.secret, 'NEWSECRET123');
        expect(result.qrCodeUrl, testMfaSetup.qrCodeUrl);
        expect(result.backupCodes, ['111111', '222222']);
        expect(result.isEnabled, true);
      });

      test('should preserve original values when no changes', () {
        // Act
        final result = testMfaSetup.copyWith();

        // Assert
        expect(result, testMfaSetup);
      });
    });

    group('equality', () {
      test('should be equal when all properties match', () {
        // Arrange
        const other = MfaSetupModel(
          secret: 'JBSWY3DPEHPK3PXP',
          qrCodeUrl: 'otpauth://totp/MeditatingLeo:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=MeditatingLeo',
          backupCodes: ['123456', '789012', '345678'],
          isEnabled: false,
        );

        // Assert
        expect(testMfaSetup, other);
        expect(testMfaSetup.hashCode, other.hashCode);
      });

      test('should not be equal when properties differ', () {
        // Arrange
        final other = testMfaSetup.copyWith(secret: 'DIFFERENTSECRET');

        // Assert
        expect(testMfaSetup, isNot(other));
        expect(testMfaSetup.hashCode, isNot(other.hashCode));
      });
    });

    group('validation', () {
      test('should validate secret format correctly', () {
        // Assert
        expect(testMfaSetup.hasValidSecret, true);
      });

      test('should identify invalid secret', () {
        // Arrange
        final invalidMfa = testMfaSetup.copyWith(secret: '');

        // Assert
        expect(invalidMfa.hasValidSecret, false);
      });

      test('should validate QR code URL format', () {
        // Assert
        expect(testMfaSetup.hasValidQrCode, true);
      });

      test('should identify invalid QR code URL', () {
        // Arrange
        final invalidMfa = testMfaSetup.copyWith(qrCodeUrl: 'invalid-url');

        // Assert
        expect(invalidMfa.hasValidQrCode, false);
      });

      test('should validate backup codes count', () {
        // Assert
        expect(testMfaSetup.hasValidBackupCodes, true);
      });

      test('should identify insufficient backup codes', () {
        // Arrange
        final invalidMfa = testMfaSetup.copyWith(backupCodes: ['123456']);

        // Assert
        expect(invalidMfa.hasValidBackupCodes, false);
      });

      test('should validate complete setup', () {
        // Assert
        expect(testMfaSetup.isValidSetup, true);
      });

      test('should identify incomplete setup', () {
        // Arrange
        final incompleteMfa = testMfaSetup.copyWith(
          secret: '',
          backupCodes: [],
        );

        // Assert
        expect(incompleteMfa.isValidSetup, false);
      });
    });

    group('backup codes management', () {
      test('should count backup codes correctly', () {
        // Assert
        expect(testMfaSetup.backupCodesCount, 3);
      });

      test('should identify if backup codes are available', () {
        // Assert
        expect(testMfaSetup.hasBackupCodes, true);
      });

      test('should handle empty backup codes', () {
        // Arrange
        final noBackupCodes = testMfaSetup.copyWith(backupCodes: []);

        // Assert
        expect(noBackupCodes.hasBackupCodes, false);
        expect(noBackupCodes.backupCodesCount, 0);
      });
    });

    group('security validation', () {
      test('should validate secret length', () {
        // Arrange
        final shortSecret = testMfaSetup.copyWith(secret: 'SHORT');
        final validSecret = testMfaSetup.copyWith(secret: 'JBSWY3DPEHPK3PXP');

        // Assert
        expect(shortSecret.hasValidSecret, false);
        expect(validSecret.hasValidSecret, true);
      });

      test('should validate backup code format', () {
        // Arrange
        final validCodes = ['123456', '789012', '345678'];
        final invalidCodes = ['123', '789012345', 'abcdef'];

        final validMfa = testMfaSetup.copyWith(backupCodes: validCodes);
        final invalidMfa = testMfaSetup.copyWith(backupCodes: invalidCodes);

        // Assert
        expect(validMfa.hasValidBackupCodes, true);
        expect(invalidMfa.hasValidBackupCodes, false);
      });
    });
  });
}
