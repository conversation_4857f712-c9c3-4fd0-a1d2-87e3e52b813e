import 'package:flutter_test/flutter_test.dart';
import 'package:meditatingleo_admin/features/auth/data/models/auth_response_model.dart';
import 'package:meditatingleo_admin/features/auth/data/models/admin_user_model.dart';

void main() {
  group('AuthResponseModel', () {
    const testUser = AdminUserModel(
      id: 'test-id',
      email: '<EMAIL>',
      role: AdminRole.contentManager,
      isActive: true,
      lastLoginAt: null,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    );

    const testAuthResponse = AuthResponseModel(
      user: testUser,
      accessToken: 'test-access-token',
      refreshToken: 'test-refresh-token',
      expiresAt: '2024-01-01T01:00:00Z',
      requiresMfa: false,
      mfaToken: null,
    );

    group('fromJson', () {
      test('should create AuthResponseModel from valid JSON without M<PERSON>', () {
        // Arrange
        final json = {
          'user': {
            'id': 'test-id',
            'email': '<EMAIL>',
            'role': 'content_manager',
            'is_active': true,
            'last_login_at': null,
            'created_at': '2024-01-01T00:00:00Z',
            'updated_at': '2024-01-01T00:00:00Z',
          },
          'access_token': 'test-access-token',
          'refresh_token': 'test-refresh-token',
          'expires_at': '2024-01-01T01:00:00Z',
          'requires_mfa': false,
          'mfa_token': null,
        };

        // Act
        final result = AuthResponseModel.fromJson(json);

        // Assert
        expect(result.user.id, 'test-id');
        expect(result.user.email, '<EMAIL>');
        expect(result.accessToken, 'test-access-token');
        expect(result.refreshToken, 'test-refresh-token');
        expect(result.expiresAt, '2024-01-01T01:00:00Z');
        expect(result.requiresMfa, false);
        expect(result.mfaToken, null);
      });

      test('should create AuthResponseModel from valid JSON with MFA required', () {
        // Arrange
        final json = {
          'user': {
            'id': 'test-id',
            'email': '<EMAIL>',
            'role': 'system_admin',
            'is_active': true,
            'last_login_at': null,
            'created_at': '2024-01-01T00:00:00Z',
            'updated_at': '2024-01-01T00:00:00Z',
          },
          'access_token': 'test-access-token',
          'refresh_token': 'test-refresh-token',
          'expires_at': '2024-01-01T01:00:00Z',
          'requires_mfa': true,
          'mfa_token': 'mfa-challenge-token',
        };

        // Act
        final result = AuthResponseModel.fromJson(json);

        // Assert
        expect(result.user.role, AdminRole.systemAdmin);
        expect(result.requiresMfa, true);
        expect(result.mfaToken, 'mfa-challenge-token');
      });
    });

    group('toJson', () {
      test('should convert AuthResponseModel to JSON correctly', () {
        // Act
        final result = testAuthResponse.toJson();

        // Assert
        expect(result['user'], isA<Map<String, dynamic>>());
        expect(result['access_token'], 'test-access-token');
        expect(result['refresh_token'], 'test-refresh-token');
        expect(result['expires_at'], '2024-01-01T01:00:00Z');
        expect(result['requires_mfa'], false);
        expect(result['mfa_token'], null);
      });

      test('should convert AuthResponseModel with MFA to JSON correctly', () {
        // Arrange
        final authResponseWithMfa = testAuthResponse.copyWith(
          requiresMfa: true,
          mfaToken: 'mfa-challenge-token',
        );

        // Act
        final result = authResponseWithMfa.toJson();

        // Assert
        expect(result['requires_mfa'], true);
        expect(result['mfa_token'], 'mfa-challenge-token');
      });
    });

    group('copyWith', () {
      test('should create copy with updated fields', () {
        // Act
        final result = testAuthResponse.copyWith(
          accessToken: 'new-access-token',
          requiresMfa: true,
          mfaToken: 'new-mfa-token',
        );

        // Assert
        expect(result.user, testAuthResponse.user);
        expect(result.accessToken, 'new-access-token');
        expect(result.refreshToken, testAuthResponse.refreshToken);
        expect(result.expiresAt, testAuthResponse.expiresAt);
        expect(result.requiresMfa, true);
        expect(result.mfaToken, 'new-mfa-token');
      });

      test('should preserve original values when no changes', () {
        // Act
        final result = testAuthResponse.copyWith();

        // Assert
        expect(result, testAuthResponse);
      });
    });

    group('equality', () {
      test('should be equal when all properties match', () {
        // Arrange
        const other = AuthResponseModel(
          user: testUser,
          accessToken: 'test-access-token',
          refreshToken: 'test-refresh-token',
          expiresAt: '2024-01-01T01:00:00Z',
          requiresMfa: false,
          mfaToken: null,
        );

        // Assert
        expect(testAuthResponse, other);
        expect(testAuthResponse.hashCode, other.hashCode);
      });

      test('should not be equal when properties differ', () {
        // Arrange
        final other = testAuthResponse.copyWith(accessToken: 'different-token');

        // Assert
        expect(testAuthResponse, isNot(other));
        expect(testAuthResponse.hashCode, isNot(other.hashCode));
      });
    });

    group('token validation', () {
      test('should identify valid tokens', () {
        // Assert
        expect(testAuthResponse.hasValidTokens, true);
      });

      test('should identify invalid tokens when access token is empty', () {
        // Arrange
        final invalidAuth = testAuthResponse.copyWith(accessToken: '');

        // Assert
        expect(invalidAuth.hasValidTokens, false);
      });

      test('should identify invalid tokens when refresh token is empty', () {
        // Arrange
        final invalidAuth = testAuthResponse.copyWith(refreshToken: '');

        // Assert
        expect(invalidAuth.hasValidTokens, false);
      });
    });

    group('MFA status', () {
      test('should identify when MFA is required', () {
        // Arrange
        final mfaRequired = testAuthResponse.copyWith(
          requiresMfa: true,
          mfaToken: 'mfa-token',
        );

        // Assert
        expect(mfaRequired.requiresMfa, true);
        expect(mfaRequired.hasMfaToken, true);
      });

      test('should identify when MFA is not required', () {
        // Assert
        expect(testAuthResponse.requiresMfa, false);
        expect(testAuthResponse.hasMfaToken, false);
      });

      test('should handle MFA token presence correctly', () {
        // Arrange
        final mfaWithToken = testAuthResponse.copyWith(
          requiresMfa: true,
          mfaToken: 'valid-mfa-token',
        );
        final mfaWithoutToken = testAuthResponse.copyWith(
          requiresMfa: true,
          mfaToken: null,
        );

        // Assert
        expect(mfaWithToken.hasMfaToken, true);
        expect(mfaWithoutToken.hasMfaToken, false);
      });
    });
  });
}
