import 'package:flutter_test/flutter_test.dart';
import 'package:meditatingleo_admin/features/auth/data/models/audit_log_model.dart';

void main() {
  group('AuditLogModel', () {
    const testAuditLog = AuditLogModel(
      id: 'test-id',
      userId: 'user-123',
      action: AuditAction.login,
      details: {'ip_address': '***********', 'user_agent': 'Chrome/91.0'},
      timestamp: '2024-01-01T12:00:00Z',
      success: true,
      errorMessage: null,
    );

    group('fromJson', () {
      test('should create AuditLogModel from valid JSON for successful login', () {
        // Arrange
        final json = {
          'id': 'test-id',
          'user_id': 'user-123',
          'action': 'login',
          'details': {'ip_address': '***********', 'user_agent': 'Chrome/91.0'},
          'timestamp': '2024-01-01T12:00:00Z',
          'success': true,
          'error_message': null,
        };

        // Act
        final result = AuditLogModel.fromJson(json);

        // Assert
        expect(result.id, 'test-id');
        expect(result.userId, 'user-123');
        expect(result.action, AuditAction.login);
        expect(result.details['ip_address'], '***********');
        expect(result.details['user_agent'], 'Chrome/91.0');
        expect(result.timestamp, '2024-01-01T12:00:00Z');
        expect(result.success, true);
        expect(result.errorMessage, null);
      });

      test('should create AuditLogModel from valid JSON for failed login', () {
        // Arrange
        final json = {
          'id': 'test-id',
          'user_id': 'user-123',
          'action': 'login_failed',
          'details': {'ip_address': '***********', 'reason': 'invalid_password'},
          'timestamp': '2024-01-01T12:00:00Z',
          'success': false,
          'error_message': 'Invalid credentials provided',
        };

        // Act
        final result = AuditLogModel.fromJson(json);

        // Assert
        expect(result.action, AuditAction.loginFailed);
        expect(result.success, false);
        expect(result.errorMessage, 'Invalid credentials provided');
        expect(result.details['reason'], 'invalid_password');
      });

      test('should handle all audit action types correctly', () {
        final actions = [
          ('login', AuditAction.login),
          ('logout', AuditAction.logout),
          ('login_failed', AuditAction.loginFailed),
          ('mfa_setup', AuditAction.mfaSetup),
          ('mfa_verified', AuditAction.mfaVerified),
          ('mfa_failed', AuditAction.mfaFailed),
          ('password_changed', AuditAction.passwordChanged),
          ('role_changed', AuditAction.roleChanged),
          ('account_locked', AuditAction.accountLocked),
          ('account_unlocked', AuditAction.accountUnlocked),
        ];

        for (final (jsonAction, expectedAction) in actions) {
          // Arrange
          final json = {
            'id': 'test-id',
            'user_id': 'user-123',
            'action': jsonAction,
            'details': <String, dynamic>{},
            'timestamp': '2024-01-01T12:00:00Z',
            'success': true,
            'error_message': null,
          };

          // Act
          final result = AuditLogModel.fromJson(json);

          // Assert
          expect(result.action, expectedAction, reason: 'Failed for action: $jsonAction');
        }
      });
    });

    group('toJson', () {
      test('should convert AuditLogModel to JSON correctly', () {
        // Act
        final result = testAuditLog.toJson();

        // Assert
        expect(result['id'], 'test-id');
        expect(result['user_id'], 'user-123');
        expect(result['action'], 'login');
        expect(result['details'], {'ip_address': '***********', 'user_agent': 'Chrome/91.0'});
        expect(result['timestamp'], '2024-01-01T12:00:00Z');
        expect(result['success'], true);
        expect(result['error_message'], null);
      });

      test('should convert failed audit log to JSON correctly', () {
        // Arrange
        final failedLog = testAuditLog.copyWith(
          action: AuditAction.loginFailed,
          success: false,
          errorMessage: 'Authentication failed',
        );

        // Act
        final result = failedLog.toJson();

        // Assert
        expect(result['action'], 'login_failed');
        expect(result['success'], false);
        expect(result['error_message'], 'Authentication failed');
      });
    });

    group('copyWith', () {
      test('should create copy with updated fields', () {
        // Act
        final result = testAuditLog.copyWith(
          action: AuditAction.logout,
          success: false,
          errorMessage: 'Session expired',
          details: {'reason': 'timeout'},
        );

        // Assert
        expect(result.id, testAuditLog.id);
        expect(result.userId, testAuditLog.userId);
        expect(result.action, AuditAction.logout);
        expect(result.details, {'reason': 'timeout'});
        expect(result.timestamp, testAuditLog.timestamp);
        expect(result.success, false);
        expect(result.errorMessage, 'Session expired');
      });

      test('should preserve original values when no changes', () {
        // Act
        final result = testAuditLog.copyWith();

        // Assert
        expect(result, testAuditLog);
      });
    });

    group('equality', () {
      test('should be equal when all properties match', () {
        // Arrange
        const other = AuditLogModel(
          id: 'test-id',
          userId: 'user-123',
          action: AuditAction.login,
          details: {'ip_address': '***********', 'user_agent': 'Chrome/91.0'},
          timestamp: '2024-01-01T12:00:00Z',
          success: true,
          errorMessage: null,
        );

        // Assert
        expect(testAuditLog, other);
        expect(testAuditLog.hashCode, other.hashCode);
      });

      test('should not be equal when properties differ', () {
        // Arrange
        final other = testAuditLog.copyWith(action: AuditAction.logout);

        // Assert
        expect(testAuditLog, isNot(other));
        expect(testAuditLog.hashCode, isNot(other.hashCode));
      });
    });

    group('audit log analysis', () {
      test('should identify security-related actions', () {
        final securityActions = [
          AuditAction.loginFailed,
          AuditAction.mfaFailed,
          AuditAction.accountLocked,
          AuditAction.passwordChanged,
          AuditAction.roleChanged,
        ];

        for (final action in securityActions) {
          final log = testAuditLog.copyWith(action: action);
          expect(log.isSecurityEvent, true, reason: 'Failed for action: $action');
        }
      });

      test('should identify non-security actions', () {
        final nonSecurityActions = [
          AuditAction.login,
          AuditAction.logout,
          AuditAction.mfaSetup,
          AuditAction.mfaVerified,
          AuditAction.accountUnlocked,
        ];

        for (final action in nonSecurityActions) {
          final log = testAuditLog.copyWith(action: action);
          expect(log.isSecurityEvent, false, reason: 'Failed for action: $action');
        }
      });

      test('should identify authentication-related actions', () {
        final authActions = [
          AuditAction.login,
          AuditAction.logout,
          AuditAction.loginFailed,
          AuditAction.mfaVerified,
          AuditAction.mfaFailed,
        ];

        for (final action in authActions) {
          final log = testAuditLog.copyWith(action: action);
          expect(log.isAuthenticationEvent, true, reason: 'Failed for action: $action');
        }
      });

      test('should identify MFA-related actions', () {
        final mfaActions = [
          AuditAction.mfaSetup,
          AuditAction.mfaVerified,
          AuditAction.mfaFailed,
        ];

        for (final action in mfaActions) {
          final log = testAuditLog.copyWith(action: action);
          expect(log.isMfaEvent, true, reason: 'Failed for action: $action');
        }
      });
    });

    group('details extraction', () {
      test('should extract IP address from details', () {
        // Assert
        expect(testAuditLog.ipAddress, '***********');
      });

      test('should extract user agent from details', () {
        // Assert
        expect(testAuditLog.userAgent, 'Chrome/91.0');
      });

      test('should handle missing IP address', () {
        // Arrange
        final logWithoutIp = testAuditLog.copyWith(
          details: {'user_agent': 'Chrome/91.0'},
        );

        // Assert
        expect(logWithoutIp.ipAddress, null);
      });

      test('should handle missing user agent', () {
        // Arrange
        final logWithoutUserAgent = testAuditLog.copyWith(
          details: {'ip_address': '***********'},
        );

        // Assert
        expect(logWithoutUserAgent.userAgent, null);
      });
    });

    group('severity assessment', () {
      test('should identify high severity events', () {
        final highSeverityActions = [
          AuditAction.loginFailed,
          AuditAction.mfaFailed,
          AuditAction.accountLocked,
          AuditAction.roleChanged,
        ];

        for (final action in highSeverityActions) {
          final log = testAuditLog.copyWith(action: action, success: false);
          expect(log.severity, AuditSeverity.high, reason: 'Failed for action: $action');
        }
      });

      test('should identify medium severity events', () {
        final mediumSeverityActions = [
          AuditAction.passwordChanged,
          AuditAction.mfaSetup,
          AuditAction.accountUnlocked,
        ];

        for (final action in mediumSeverityActions) {
          final log = testAuditLog.copyWith(action: action);
          expect(log.severity, AuditSeverity.medium, reason: 'Failed for action: $action');
        }
      });

      test('should identify low severity events', () {
        final lowSeverityActions = [
          AuditAction.login,
          AuditAction.logout,
          AuditAction.mfaVerified,
        ];

        for (final action in lowSeverityActions) {
          final log = testAuditLog.copyWith(action: action);
          expect(log.severity, AuditSeverity.low, reason: 'Failed for action: $action');
        }
      });
    });
  });
}
